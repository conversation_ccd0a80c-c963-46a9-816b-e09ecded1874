namespace GameItemManager.Forms
{
    partial class ItemEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.propertyGrid1 = new PropertyGrid();
            this.panel1 = new Panel();
            this.buttonOK = new Button();
            this.buttonCancel = new Button();
            this.buttonApply = new Button();
            this.labelTitle = new Label();
            
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // propertyGrid1
            // 
            this.propertyGrid1.Dock = DockStyle.Fill;
            this.propertyGrid1.Location = new Point(0, 40);
            this.propertyGrid1.Name = "propertyGrid1";
            this.propertyGrid1.Size = new Size(800, 510);
            this.propertyGrid1.TabIndex = 0;
            this.propertyGrid1.PropertyValueChanged += new PropertyValueChangedEventHandler(this.PropertyGrid1_PropertyValueChanged);
            
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.buttonApply);
            this.panel1.Controls.Add(this.buttonCancel);
            this.panel1.Controls.Add(this.buttonOK);
            this.panel1.Dock = DockStyle.Bottom;
            this.panel1.Location = new Point(0, 550);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(800, 50);
            this.panel1.TabIndex = 1;
            
            // 
            // buttonOK
            // 
            this.buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonOK.Location = new Point(550, 15);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new Size(75, 23);
            this.buttonOK.TabIndex = 0;
            this.buttonOK.Text = "确定(&O)";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new EventHandler(this.ButtonOK_Click);
            
            // 
            // buttonCancel
            // 
            this.buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonCancel.DialogResult = DialogResult.Cancel;
            this.buttonCancel.Location = new Point(631, 15);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new Size(75, 23);
            this.buttonCancel.TabIndex = 1;
            this.buttonCancel.Text = "取消(&C)";
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new EventHandler(this.ButtonCancel_Click);
            
            // 
            // buttonApply
            // 
            this.buttonApply.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonApply.Location = new Point(712, 15);
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Size = new Size(75, 23);
            this.buttonApply.TabIndex = 2;
            this.buttonApply.Text = "应用(&A)";
            this.buttonApply.UseVisualStyleBackColor = true;
            this.buttonApply.Click += new EventHandler(this.ButtonApply_Click);
            
            // 
            // labelTitle
            // 
            this.labelTitle.Dock = DockStyle.Top;
            this.labelTitle.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            this.labelTitle.Location = new Point(0, 0);
            this.labelTitle.Name = "labelTitle";
            this.labelTitle.Size = new Size(800, 40);
            this.labelTitle.TabIndex = 2;
            this.labelTitle.Text = "物品编辑器";
            this.labelTitle.TextAlign = ContentAlignment.MiddleCenter;
            
            // 
            // ItemEditForm
            // 
            this.AcceptButton = this.buttonOK;
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.CancelButton = this.buttonCancel;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.propertyGrid1);
            this.Controls.Add(this.labelTitle);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ItemEditForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "物品编辑器";
            this.FormClosing += new FormClosingEventHandler(this.ItemEditForm_FormClosing);
            
            this.panel1.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private PropertyGrid propertyGrid1;
        private Panel panel1;
        private Button buttonOK;
        private Button buttonCancel;
        private Button buttonApply;
        private Label labelTitle;
    }
}
