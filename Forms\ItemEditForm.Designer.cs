namespace GameItemManager.Forms
{
    partial class ItemEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.propertyGrid1 = new PropertyGrid();
            this.panel1 = new Panel();
            this.buttonOK = new Button();
            this.buttonCancel = new Button();
            this.buttonApply = new Button();
            this.labelTitle = new Label();

            this.panel1.SuspendLayout();
            this.SuspendLayout();

            //
            // propertyGrid1
            //
            this.propertyGrid1.Dock = DockStyle.Fill;
            this.propertyGrid1.Location = new Point(0, 40);
            this.propertyGrid1.Name = "propertyGrid1";
            this.propertyGrid1.Size = new Size(800, 510);
            this.propertyGrid1.TabIndex = 0;
            this.propertyGrid1.PropertyValueChanged += new PropertyValueChangedEventHandler(this.PropertyGrid1_PropertyValueChanged);

            //
            // panel1
            //
            this.panel1.Controls.Add(this.buttonApply);
            this.panel1.Controls.Add(this.buttonCancel);
            this.panel1.Controls.Add(this.buttonOK);
            this.panel1.Dock = DockStyle.Bottom;
            this.panel1.Location = new Point(0, 550);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(800, 50);
            this.panel1.TabIndex = 1;

            //
            // buttonOK
            //
            this.buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonOK.Location = new Point(550, 15);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new Size(75, 23);
            this.buttonOK.TabIndex = 0;
            this.buttonOK.Text = "确定(&O)";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new EventHandler(this.ButtonOK_Click);

            //
            // buttonCancel
            //
            this.buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonCancel.DialogResult = DialogResult.Cancel;
            this.buttonCancel.Location = new Point(631, 15);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new Size(75, 23);
            this.buttonCancel.TabIndex = 1;
            this.buttonCancel.Text = "取消(&C)";
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new EventHandler(this.ButtonCancel_Click);

            //
            // buttonApply
            //
            this.buttonApply.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.buttonApply.Location = new Point(712, 15);
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Size = new Size(75, 23);
            this.buttonApply.TabIndex = 2;
            this.buttonApply.Text = "应用(&A)";
            this.buttonApply.UseVisualStyleBackColor = true;
            this.buttonApply.Click += new EventHandler(this.ButtonApply_Click);

            //
            // labelTitle
            //
            this.labelTitle.Dock = DockStyle.Top;
            this.labelTitle.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point);
            this.labelTitle.Location = new Point(0, 0);
            this.labelTitle.Name = "labelTitle";
            this.labelTitle.Size = new Size(800, 40);
            this.labelTitle.TabIndex = 2;
            this.labelTitle.Text = "物品编辑器";
            this.labelTitle.TextAlign = ContentAlignment.MiddleCenter;

            //
            // MainForm
            //
            this.AcceptButton = this.buttonOK;
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.CancelButton = this.buttonCancel;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.propertyGrid1);
            this.Controls.Add(this.labelTitle);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ItemEditForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "物品编辑器";
            this.FormClosing += new FormClosingEventHandler(this.ItemEditForm_FormClosing);

            this.panel1.ResumeLayout(false);
            this.ResumeLayout(false);

            //
            // tabControl1
            //
            this.tabControl1.Controls.Add(this.tabPageBasic);
            this.tabControl1.Controls.Add(this.tabPageAttributes);
            this.tabControl1.Controls.Add(this.tabPageCombat);
            this.tabControl1.Controls.Add(this.tabPageClasses);
            this.tabControl1.Controls.Add(this.tabPageOther);
            this.tabControl1.Dock = DockStyle.Fill;
            this.tabControl1.Location = new Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new Size(800, 520);
            this.tabControl1.TabIndex = 0;

            //
            // tabPageBasic
            //
            this.tabPageBasic.Controls.Add(this.labelId);
            this.tabPageBasic.Controls.Add(this.textBoxId);
            this.tabPageBasic.Controls.Add(this.labelName);
            this.tabPageBasic.Controls.Add(this.textBoxName);
            this.tabPageBasic.Controls.Add(this.labelDesc);
            this.tabPageBasic.Controls.Add(this.textBoxDesc);
            this.tabPageBasic.Controls.Add(this.labelDescText);
            this.tabPageBasic.Controls.Add(this.textBoxDescText);
            this.tabPageBasic.Controls.Add(this.labelWeaponType);
            this.tabPageBasic.Controls.Add(this.comboBoxWeaponType);
            this.tabPageBasic.Controls.Add(this.labelItemType);
            this.tabPageBasic.Controls.Add(this.comboBoxItemType);
            this.tabPageBasic.Controls.Add(this.labelAttackType);
            this.tabPageBasic.Controls.Add(this.comboBoxAttackType);
            this.tabPageBasic.Controls.Add(this.labelQuality);
            this.tabPageBasic.Controls.Add(this.comboBoxQuality);
            this.tabPageBasic.Controls.Add(this.labelLevel);
            this.tabPageBasic.Controls.Add(this.numericUpDownLevel);
            this.tabPageBasic.Controls.Add(this.labelPrice);
            this.tabPageBasic.Controls.Add(this.numericUpDownPrice);
            this.tabPageBasic.Controls.Add(this.labelMaxStackCount);
            this.tabPageBasic.Controls.Add(this.numericUpDownMaxStackCount);
            this.tabPageBasic.Controls.Add(this.labelMesh);
            this.tabPageBasic.Controls.Add(this.textBoxMesh);
            this.tabPageBasic.Controls.Add(this.labelMaterial);
            this.tabPageBasic.Controls.Add(this.textBoxMaterial);
            this.tabPageBasic.Controls.Add(this.labelIconName);
            this.tabPageBasic.Controls.Add(this.textBoxIconName);
            this.tabPageBasic.Controls.Add(this.labelUiSoundType);
            this.tabPageBasic.Controls.Add(this.textBoxUiSoundType);
            this.tabPageBasic.Controls.Add(this.labelMinDamage);
            this.tabPageBasic.Controls.Add(this.numericUpDownMinDamage);
            this.tabPageBasic.Controls.Add(this.labelMaxDamage);
            this.tabPageBasic.Controls.Add(this.numericUpDownMaxDamage);
            this.tabPageBasic.Location = new Point(4, 24);
            this.tabPageBasic.Name = "tabPageBasic";
            this.tabPageBasic.Padding = new Padding(3);
            this.tabPageBasic.Size = new Size(792, 492);
            this.tabPageBasic.TabIndex = 0;
            this.tabPageBasic.Text = "基本信息";
            this.tabPageBasic.UseVisualStyleBackColor = true;

            // 基本信息控件布局
            this.labelId.AutoSize = true;
            this.labelId.Location = new Point(20, 20);
            this.labelId.Name = "labelId";
            this.labelId.Size = new Size(68, 15);
            this.labelId.TabIndex = 0;
            this.labelId.Text = "物品ID:";

            this.textBoxId.Location = new Point(120, 17);
            this.textBoxId.Name = "textBoxId";
            this.textBoxId.Size = new Size(200, 23);
            this.textBoxId.TabIndex = 1;

            this.labelName.AutoSize = true;
            this.labelName.Location = new Point(20, 50);
            this.labelName.Name = "labelName";
            this.labelName.Size = new Size(68, 15);
            this.labelName.TabIndex = 2;
            this.labelName.Text = "物品名称:";

            this.textBoxName.Location = new Point(120, 47);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.Size = new Size(200, 23);
            this.textBoxName.TabIndex = 3;

            this.labelDesc.AutoSize = true;
            this.labelDesc.Location = new Point(20, 80);
            this.labelDesc.Name = "labelDesc";
            this.labelDesc.Size = new Size(92, 15);
            this.labelDesc.TabIndex = 4;
            this.labelDesc.Text = "描述字符串ID:";

            this.textBoxDesc.Location = new Point(120, 77);
            this.textBoxDesc.Name = "textBoxDesc";
            this.textBoxDesc.Size = new Size(200, 23);
            this.textBoxDesc.TabIndex = 5;

            this.labelDescText.AutoSize = true;
            this.labelDescText.Location = new Point(20, 110);
            this.labelDescText.Name = "labelDescText";
            this.labelDescText.Size = new Size(80, 15);
            this.labelDescText.TabIndex = 6;
            this.labelDescText.Text = "描述中文文本:";

            this.textBoxDescText.Location = new Point(120, 107);
            this.textBoxDescText.Multiline = true;
            this.textBoxDescText.Name = "textBoxDescText";
            this.textBoxDescText.Size = new Size(300, 60);
            this.textBoxDescText.TabIndex = 7;

            this.labelWeaponType.AutoSize = true;
            this.labelWeaponType.Location = new Point(20, 180);
            this.labelWeaponType.Name = "labelWeaponType";
            this.labelWeaponType.Size = new Size(68, 15);
            this.labelWeaponType.TabIndex = 8;
            this.labelWeaponType.Text = "武器类型:";

            this.comboBoxWeaponType.FormattingEnabled = true;
            this.comboBoxWeaponType.Items.AddRange(new object[] {
                "1h_sword", "2h_sword", "dagger", "mace", "orb", "book", "polearm", "staff", "bow"});
            this.comboBoxWeaponType.Location = new Point(120, 177);
            this.comboBoxWeaponType.Name = "comboBoxWeaponType";
            this.comboBoxWeaponType.Size = new Size(200, 23);
            this.comboBoxWeaponType.TabIndex = 9;

            this.labelItemType.AutoSize = true;
            this.labelItemType.Location = new Point(20, 210);
            this.labelItemType.Name = "labelItemType";
            this.labelItemType.Size = new Size(68, 15);
            this.labelItemType.TabIndex = 10;
            this.labelItemType.Text = "物品类型:";

            this.comboBoxItemType.FormattingEnabled = true;
            this.comboBoxItemType.Items.AddRange(new object[] {
                "normal", "abyss", "draconic"});
            this.comboBoxItemType.Location = new Point(120, 207);
            this.comboBoxItemType.Name = "comboBoxItemType";
            this.comboBoxItemType.Size = new Size(200, 23);
            this.comboBoxItemType.TabIndex = 11;

            this.labelAttackType.AutoSize = true;
            this.labelAttackType.Location = new Point(20, 240);
            this.labelAttackType.Name = "labelAttackType";
            this.labelAttackType.Size = new Size(68, 15);
            this.labelAttackType.TabIndex = 12;
            this.labelAttackType.Text = "攻击类型:";

            this.comboBoxAttackType.FormattingEnabled = true;
            this.comboBoxAttackType.Items.AddRange(new object[] {
                "physical", "magical"});
            this.comboBoxAttackType.Location = new Point(120, 237);
            this.comboBoxAttackType.Name = "comboBoxAttackType";
            this.comboBoxAttackType.Size = new Size(200, 23);
            this.comboBoxAttackType.TabIndex = 13;

            this.labelQuality.AutoSize = true;
            this.labelQuality.Location = new Point(20, 270);
            this.labelQuality.Name = "labelQuality";
            this.labelQuality.Size = new Size(44, 15);
            this.labelQuality.TabIndex = 14;
            this.labelQuality.Text = "品质:";

            this.comboBoxQuality.FormattingEnabled = true;
            this.comboBoxQuality.Items.AddRange(new object[] {
                "common", "rare", "unique", "epic", "legendary"});
            this.comboBoxQuality.Location = new Point(120, 267);
            this.comboBoxQuality.Name = "comboBoxQuality";
            this.comboBoxQuality.Size = new Size(200, 23);
            this.comboBoxQuality.TabIndex = 15;

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private TabControl tabControl1;
        private TabPage tabPageBasic;
        private TabPage tabPageAttributes;
        private TabPage tabPageCombat;
        private TabPage tabPageClasses;
        private TabPage tabPageOther;
        
        private Button buttonOK;
        private Button buttonCancel;
        private Button buttonApply;
        
        // 基本信息控件声明
        private Label labelId;
        private TextBox textBoxId;
        private Label labelName;
        private TextBox textBoxName;
        private Label labelDesc;
        private TextBox textBoxDesc;
        private Label labelDescText;
        private TextBox textBoxDescText;
        private Label labelWeaponType;
        private ComboBox comboBoxWeaponType;
        private Label labelItemType;
        private ComboBox comboBoxItemType;
        private Label labelAttackType;
        private ComboBox comboBoxAttackType;
        private Label labelQuality;
        private ComboBox comboBoxQuality;
        private Label labelLevel;
        private NumericUpDown numericUpDownLevel;
        private Label labelPrice;
        private NumericUpDown numericUpDownPrice;
        private Label labelMaxStackCount;
        private NumericUpDown numericUpDownMaxStackCount;
        
        // 外观相关控件声明
        private Label labelMesh;
        private TextBox textBoxMesh;
        private Label labelMaterial;
        private TextBox textBoxMaterial;
        private Label labelIconName;
        private TextBox textBoxIconName;
        private Label labelUiSoundType;
        private TextBox textBoxUiSoundType;
        
        // 伤害相关控件声明
        private Label labelMinDamage;
        private NumericUpDown numericUpDownMinDamage;
        private Label labelMaxDamage;
        private NumericUpDown numericUpDownMaxDamage;
        
        // 基础属性控件声明
        private Label labelStr;
        private NumericUpDown numericUpDownStr;
        private Label labelAgi;
        private NumericUpDown numericUpDownAgi;
        private Label labelKno;
        private NumericUpDown numericUpDownKno;
        private Label labelVit;
        private NumericUpDown numericUpDownVit;
        private Label labelDex;
        private NumericUpDown numericUpDownDex;
        private Label labelWil;
        private NumericUpDown numericUpDownWil;
        
        // 战斗属性控件声明
        private Label labelHitAccuracy;
        private NumericUpDown numericUpDownHitAccuracy;
        private Label labelCritical;
        private NumericUpDown numericUpDownCritical;
        private Label labelParry;
        private NumericUpDown numericUpDownParry;
        private Label labelAttackDelay;
        private NumericUpDown numericUpDownAttackDelay;
        private Label labelHitCount;
        private NumericUpDown numericUpDownHitCount;
        private Label labelAttackGap;
        private NumericUpDown numericUpDownAttackGap;
        private Label labelAttackRange;
        private NumericUpDown numericUpDownAttackRange;
        
        // 布尔属性控件声明
        private CheckBox checkBoxLore;
        private CheckBox checkBoxCanExchange;
        private CheckBox checkBoxCanSellToNpc;
        private CheckBox checkBoxBreakable;
        private CheckBox checkBoxSoulBind;
        private CheckBox checkBoxNoEnchant;
        private CheckBox checkBoxCanPolish;
        
        // 职业限制控件声明
        private GroupBox groupBoxClasses;
        private CheckBox checkBoxWarrior;
        private CheckBox checkBoxScout;
        private CheckBox checkBoxMage;
        private CheckBox checkBoxCleric;
        private CheckBox checkBoxEngineer;
        private CheckBox checkBoxArtist;
        private CheckBox checkBoxFighter;
        private CheckBox checkBoxKnight;
        private CheckBox checkBoxAssassin;
        private CheckBox checkBoxRanger;
        private CheckBox checkBoxWizard;
        private CheckBox checkBoxElementalist;
        private CheckBox checkBoxChanter;
        private CheckBox checkBoxPriest;
        private CheckBox checkBoxGunner;
        private CheckBox checkBoxBard;
        private CheckBox checkBoxRider;
        
        // 其他属性控件声明
        private Label labelEquipmentSlots;
        private TextBox textBoxEquipmentSlots;
        private Label labelGenderPermitted;
        private TextBox textBoxGenderPermitted;
        private Label labelRacePermitted;
        private TextBox textBoxRacePermitted;
        private Label labelEquipBone;
        private TextBox textBoxEquipBone;
        private Label labelBonusApply;
        private TextBox textBoxBonusApply;
        private Label labelTrailTex;
        private TextBox textBoxTrailTex;
        
        // 附魔相关控件声明
        private Label labelMaxEnchantValue;
        private NumericUpDown numericUpDownMaxEnchantValue;
        private Label labelOptionSlotValue;
        private NumericUpDown numericUpDownOptionSlotValue;
        private Label labelSpecialSlotValue;
        private NumericUpDown numericUpDownSpecialSlotValue;
        private Label labelOptionSlotBonus;
        private NumericUpDown numericUpDownOptionSlotBonus;
        
        // 特效相关控件声明
        private Label labelMeshChange;
        private NumericUpDown numericUpDownMeshChange;
        private Label labelDmgDecal;
        private NumericUpDown numericUpDownDmgDecal;
        private Label labelBladeFx;
        private NumericUpDown numericUpDownBladeFx;
        
        // 抛光相关控件声明
        private Label labelPolishBurnOnAttack;
        private NumericUpDown numericUpDownPolishBurnOnAttack;
        private Label labelPolishBurnOnDefend;
        private NumericUpDown numericUpDownPolishBurnOnDefend;
        
        // 其他数值控件声明
        private Label labelCannotChangeskin;
        private NumericUpDown numericUpDownCannotChangeskin;
        private Label labelCashItem;
        private NumericUpDown numericUpDownCashItem;
        private Label labelBmRestrictCategory;
        private NumericUpDown numericUpDownBmRestrictCategory;
    }
}
