# 游戏物品管理器 (Game Item Manager)

一个用于管理XML游戏物品数据的C# WinForms应用程序，支持读取、编辑和保存游戏物品配置文件。

## 功能特性

### 核心功能
- **XML文件读取**: 支持UTF-16编码的XML文件读取，自动解析client_item结构
- **字符串资源管理**: 自动加载strings文件夹中的中文字符串资源，支持ID到中文文本的映射
- **物品编辑**: 使用PropertyGrid提供直观的物品属性编辑界面
- **搜索和过滤**: 支持按名称、ID、描述、品质、武器类型等多种方式搜索和过滤物品
- **XML文件保存**: 保持原有格式和编码，自动创建备份文件

### 用户界面特性
- **清洁易懂的界面**: 分为文件列表、物品列表和详细信息三个面板
- **高级搜索**: 提供专门的高级搜索窗体，支持多条件组合搜索
- **列表排序**: 点击列标题可按该列排序（升序/降序）
- **工具提示**: 为所有按钮和控件提供详细的使用说明
- **状态提示**: 实时显示操作状态和进度信息

### 用户体验优化
- **进度条显示**: 长时间操作显示进度条
- **错误处理**: 友好的错误提示和处理机制
- **自动备份**: 保存时自动创建备份文件
- **快捷键支持**: 支持常用操作的快捷键

## 系统要求

- Windows 10 或更高版本
- .NET 6.0 或更高版本
- 至少 100MB 可用磁盘空间

## 安装和使用

### 编译运行
1. 确保安装了 .NET 6.0 SDK
2. 克隆或下载项目代码
3. 在项目根目录运行：
   ```bash
   dotnet build
   dotnet run
   ```

### 使用说明

#### 1. 准备数据文件
确保工作目录包含以下文件结构：
```
工作目录/
├── client_items_armor.xml    # 装备物品数据
├── client_items_etc.xml      # 其他物品数据
├── client_items_misc.xml     # 杂项物品数据
└── strings/                  # 字符串资源文件夹
    ├── client_strings_item.xml
    ├── client_strings_item2.xml
    └── ...其他字符串文件
```

#### 2. 加载数据
1. 启动应用程序
2. 点击"加载XML文件"按钮或使用快捷键 Ctrl+O
3. 等待加载完成，状态栏会显示加载进度

#### 3. 浏览和搜索物品
- **基本搜索**: 在工具栏搜索框中输入关键词
- **高级搜索**: 使用菜单"编辑 > 查找"或快捷键 Ctrl+F
- **列表排序**: 点击列标题进行排序
- **查看详情**: 选择物品后在下方属性面板查看详细信息

#### 4. 编辑物品
1. 在物品列表中选择要编辑的物品
2. 双击物品或点击"编辑"按钮
3. 在弹出的编辑窗体中修改属性
4. 点击"确定"保存修改

#### 5. 保存数据
1. 完成编辑后，使用菜单"文件 > 保存全部"或快捷键 Ctrl+S
2. 确认保存操作
3. 系统会自动创建备份文件并保存修改

## 文件格式说明

### XML物品文件格式
```xml
<client_items generated_time="2024/01/01 12:00:00">
  <client_item>
    <id>100000001</id>
    <name>sword_circulous</name>
    <desc>STR_SWORD_CIRCULOUS</desc>
    <weapon_type>1h_sword</weapon_type>
    <item_type>normal</item_type>
    <!-- 更多属性... -->
  </client_item>
</client_items>
```

### 字符串资源文件格式
支持多种格式的字符串文件，程序会自动识别和解析。

## 快捷键

- **Ctrl+O**: 加载XML文件
- **Ctrl+S**: 保存所有修改
- **Ctrl+N**: 新建物品
- **Ctrl+F**: 高级搜索
- **Delete**: 删除选中物品
- **Enter**: 在搜索框中执行搜索

## 注意事项

1. **备份重要数据**: 虽然程序会自动创建备份，但建议在大量修改前手动备份重要文件
2. **文件编码**: 程序支持UTF-16编码，保存时会保持原有编码格式
3. **内存使用**: 大量物品数据可能占用较多内存，建议在配置较好的机器上运行
4. **文件锁定**: 保存时确保XML文件没有被其他程序打开

## 故障排除

### 常见问题

**Q: 程序启动后显示"字符串资源加载失败"**
A: 检查strings文件夹是否存在且包含有效的XML文件

**Q: 无法加载XML文件**
A: 确保当前目录包含client_items*.xml文件，且文件格式正确

**Q: 保存时提示权限错误**
A: 确保对工作目录有写入权限，或以管理员身份运行程序

**Q: 中文显示乱码**
A: 检查字符串资源文件的编码是否正确

## 开发信息

- **开发语言**: C# (.NET 6.0)
- **UI框架**: Windows Forms
- **主要依赖**: System.Text.Encoding.CodePages

## 版本历史

### v1.0 (当前版本)
- 基本的XML文件读取和保存功能
- 物品编辑和搜索功能
- 用户界面优化
- 错误处理和用户体验改进

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub仓库地址]
- 邮箱: [联系邮箱]
