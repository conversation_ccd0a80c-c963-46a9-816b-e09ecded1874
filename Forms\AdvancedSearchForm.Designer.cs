namespace GameItemManager.Forms
{
    partial class AdvancedSearchForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxSearchText = new GroupBox();
            this.textBoxSearchText = new TextBox();
            this.labelSearchText = new Label();
            
            this.groupBoxSearchOptions = new GroupBox();
            this.checkBoxSearchInDescription = new CheckBox();
            this.checkBoxSearchInName = new CheckBox();
            this.checkBoxSearchInId = new CheckBox();
            
            this.groupBoxFilters = new GroupBox();
            this.labelQuality = new Label();
            this.comboBoxQuality = new ComboBox();
            this.labelWeaponType = new Label();
            this.comboBoxWeaponType = new ComboBox();
            this.labelItemType = new Label();
            this.comboBoxItemType = new ComboBox();
            this.labelMinLevel = new Label();
            this.numericUpDownMinLevel = new NumericUpDown();
            this.labelMaxLevel = new Label();
            this.numericUpDownMaxLevel = new NumericUpDown();
            
            this.buttonSearch = new Button();
            this.buttonCancel = new Button();
            this.buttonReset = new Button();
            
            this.groupBoxSearchText.SuspendLayout();
            this.groupBoxSearchOptions.SuspendLayout();
            this.groupBoxFilters.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinLevel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxLevel)).BeginInit();
            this.SuspendLayout();
            
            // 
            // groupBoxSearchText
            // 
            this.groupBoxSearchText.Controls.Add(this.textBoxSearchText);
            this.groupBoxSearchText.Controls.Add(this.labelSearchText);
            this.groupBoxSearchText.Location = new Point(12, 12);
            this.groupBoxSearchText.Name = "groupBoxSearchText";
            this.groupBoxSearchText.Size = new Size(460, 60);
            this.groupBoxSearchText.TabIndex = 0;
            this.groupBoxSearchText.TabStop = false;
            this.groupBoxSearchText.Text = "搜索文本";
            
            // 
            // labelSearchText
            // 
            this.labelSearchText.AutoSize = true;
            this.labelSearchText.Location = new Point(15, 25);
            this.labelSearchText.Name = "labelSearchText";
            this.labelSearchText.Size = new Size(68, 15);
            this.labelSearchText.TabIndex = 0;
            this.labelSearchText.Text = "搜索关键词:";
            
            // 
            // textBoxSearchText
            // 
            this.textBoxSearchText.Location = new Point(90, 22);
            this.textBoxSearchText.Name = "textBoxSearchText";
            this.textBoxSearchText.Size = new Size(350, 23);
            this.textBoxSearchText.TabIndex = 1;
            
            // 
            // groupBoxSearchOptions
            // 
            this.groupBoxSearchOptions.Controls.Add(this.checkBoxSearchInDescription);
            this.groupBoxSearchOptions.Controls.Add(this.checkBoxSearchInName);
            this.groupBoxSearchOptions.Controls.Add(this.checkBoxSearchInId);
            this.groupBoxSearchOptions.Location = new Point(12, 78);
            this.groupBoxSearchOptions.Name = "groupBoxSearchOptions";
            this.groupBoxSearchOptions.Size = new Size(460, 60);
            this.groupBoxSearchOptions.TabIndex = 1;
            this.groupBoxSearchOptions.TabStop = false;
            this.groupBoxSearchOptions.Text = "搜索范围";
            
            // 
            // checkBoxSearchInId
            // 
            this.checkBoxSearchInId.AutoSize = true;
            this.checkBoxSearchInId.Location = new Point(15, 25);
            this.checkBoxSearchInId.Name = "checkBoxSearchInId";
            this.checkBoxSearchInId.Size = new Size(75, 19);
            this.checkBoxSearchInId.TabIndex = 0;
            this.checkBoxSearchInId.Text = "物品ID";
            this.checkBoxSearchInId.UseVisualStyleBackColor = true;
            
            // 
            // checkBoxSearchInName
            // 
            this.checkBoxSearchInName.AutoSize = true;
            this.checkBoxSearchInName.Location = new Point(120, 25);
            this.checkBoxSearchInName.Name = "checkBoxSearchInName";
            this.checkBoxSearchInName.Size = new Size(87, 19);
            this.checkBoxSearchInName.TabIndex = 1;
            this.checkBoxSearchInName.Text = "物品名称";
            this.checkBoxSearchInName.UseVisualStyleBackColor = true;
            
            // 
            // checkBoxSearchInDescription
            // 
            this.checkBoxSearchInDescription.AutoSize = true;
            this.checkBoxSearchInDescription.Location = new Point(240, 25);
            this.checkBoxSearchInDescription.Name = "checkBoxSearchInDescription";
            this.checkBoxSearchInDescription.Size = new Size(87, 19);
            this.checkBoxSearchInDescription.TabIndex = 2;
            this.checkBoxSearchInDescription.Text = "物品描述";
            this.checkBoxSearchInDescription.UseVisualStyleBackColor = true;
            
            // 
            // groupBoxFilters
            // 
            this.groupBoxFilters.Controls.Add(this.labelQuality);
            this.groupBoxFilters.Controls.Add(this.comboBoxQuality);
            this.groupBoxFilters.Controls.Add(this.labelWeaponType);
            this.groupBoxFilters.Controls.Add(this.comboBoxWeaponType);
            this.groupBoxFilters.Controls.Add(this.labelItemType);
            this.groupBoxFilters.Controls.Add(this.comboBoxItemType);
            this.groupBoxFilters.Controls.Add(this.labelMinLevel);
            this.groupBoxFilters.Controls.Add(this.numericUpDownMinLevel);
            this.groupBoxFilters.Controls.Add(this.labelMaxLevel);
            this.groupBoxFilters.Controls.Add(this.numericUpDownMaxLevel);
            this.groupBoxFilters.Location = new Point(12, 144);
            this.groupBoxFilters.Name = "groupBoxFilters";
            this.groupBoxFilters.Size = new Size(460, 150);
            this.groupBoxFilters.TabIndex = 2;
            this.groupBoxFilters.TabStop = false;
            this.groupBoxFilters.Text = "过滤条件";
            
            // 
            // labelQuality
            // 
            this.labelQuality.AutoSize = true;
            this.labelQuality.Location = new Point(15, 25);
            this.labelQuality.Name = "labelQuality";
            this.labelQuality.Size = new Size(44, 15);
            this.labelQuality.TabIndex = 0;
            this.labelQuality.Text = "品质:";
            
            // 
            // comboBoxQuality
            // 
            this.comboBoxQuality.DropDownStyle = ComboBoxStyle.DropDownList;
            this.comboBoxQuality.FormattingEnabled = true;
            this.comboBoxQuality.Location = new Point(70, 22);
            this.comboBoxQuality.Name = "comboBoxQuality";
            this.comboBoxQuality.Size = new Size(150, 23);
            this.comboBoxQuality.TabIndex = 1;
            
            // 
            // labelWeaponType
            // 
            this.labelWeaponType.AutoSize = true;
            this.labelWeaponType.Location = new Point(240, 25);
            this.labelWeaponType.Name = "labelWeaponType";
            this.labelWeaponType.Size = new Size(68, 15);
            this.labelWeaponType.TabIndex = 2;
            this.labelWeaponType.Text = "武器类型:";
            
            // 
            // comboBoxWeaponType
            // 
            this.comboBoxWeaponType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.comboBoxWeaponType.FormattingEnabled = true;
            this.comboBoxWeaponType.Location = new Point(315, 22);
            this.comboBoxWeaponType.Name = "comboBoxWeaponType";
            this.comboBoxWeaponType.Size = new Size(130, 23);
            this.comboBoxWeaponType.TabIndex = 3;
            
            // 
            // labelItemType
            // 
            this.labelItemType.AutoSize = true;
            this.labelItemType.Location = new Point(15, 60);
            this.labelItemType.Name = "labelItemType";
            this.labelItemType.Size = new Size(68, 15);
            this.labelItemType.TabIndex = 4;
            this.labelItemType.Text = "物品类型:";
            
            // 
            // comboBoxItemType
            // 
            this.comboBoxItemType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.comboBoxItemType.FormattingEnabled = true;
            this.comboBoxItemType.Location = new Point(90, 57);
            this.comboBoxItemType.Name = "comboBoxItemType";
            this.comboBoxItemType.Size = new Size(150, 23);
            this.comboBoxItemType.TabIndex = 5;
            
            // 
            // labelMinLevel
            // 
            this.labelMinLevel.AutoSize = true;
            this.labelMinLevel.Location = new Point(15, 95);
            this.labelMinLevel.Name = "labelMinLevel";
            this.labelMinLevel.Size = new Size(68, 15);
            this.labelMinLevel.TabIndex = 6;
            this.labelMinLevel.Text = "最小等级:";
            
            // 
            // numericUpDownMinLevel
            // 
            this.numericUpDownMinLevel.Location = new Point(90, 92);
            this.numericUpDownMinLevel.Maximum = new decimal(new int[] { 999, 0, 0, 0 });
            this.numericUpDownMinLevel.Name = "numericUpDownMinLevel";
            this.numericUpDownMinLevel.Size = new Size(80, 23);
            this.numericUpDownMinLevel.TabIndex = 7;
            
            // 
            // labelMaxLevel
            // 
            this.labelMaxLevel.AutoSize = true;
            this.labelMaxLevel.Location = new Point(200, 95);
            this.labelMaxLevel.Name = "labelMaxLevel";
            this.labelMaxLevel.Size = new Size(68, 15);
            this.labelMaxLevel.TabIndex = 8;
            this.labelMaxLevel.Text = "最大等级:";
            
            // 
            // numericUpDownMaxLevel
            // 
            this.numericUpDownMaxLevel.Location = new Point(275, 92);
            this.numericUpDownMaxLevel.Maximum = new decimal(new int[] { 999, 0, 0, 0 });
            this.numericUpDownMaxLevel.Name = "numericUpDownMaxLevel";
            this.numericUpDownMaxLevel.Size = new Size(80, 23);
            this.numericUpDownMaxLevel.TabIndex = 9;
            this.numericUpDownMaxLevel.Value = new decimal(new int[] { 999, 0, 0, 0 });
            
            // 
            // buttonSearch
            // 
            this.buttonSearch.Location = new Point(240, 310);
            this.buttonSearch.Name = "buttonSearch";
            this.buttonSearch.Size = new Size(75, 30);
            this.buttonSearch.TabIndex = 3;
            this.buttonSearch.Text = "搜索(&S)";
            this.buttonSearch.UseVisualStyleBackColor = true;
            this.buttonSearch.Click += new EventHandler(this.ButtonSearch_Click);
            
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = DialogResult.Cancel;
            this.buttonCancel.Location = new Point(321, 310);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new Size(75, 30);
            this.buttonCancel.TabIndex = 4;
            this.buttonCancel.Text = "取消(&C)";
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new EventHandler(this.ButtonCancel_Click);
            
            // 
            // buttonReset
            // 
            this.buttonReset.Location = new Point(402, 310);
            this.buttonReset.Name = "buttonReset";
            this.buttonReset.Size = new Size(75, 30);
            this.buttonReset.TabIndex = 5;
            this.buttonReset.Text = "重置(&R)";
            this.buttonReset.UseVisualStyleBackColor = true;
            this.buttonReset.Click += new EventHandler(this.ButtonReset_Click);
            
            // 
            // AdvancedSearchForm
            // 
            this.AcceptButton = this.buttonSearch;
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.CancelButton = this.buttonCancel;
            this.ClientSize = new Size(484, 352);
            this.Controls.Add(this.buttonReset);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonSearch);
            this.Controls.Add(this.groupBoxFilters);
            this.Controls.Add(this.groupBoxSearchOptions);
            this.Controls.Add(this.groupBoxSearchText);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AdvancedSearchForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "高级搜索";
            
            this.groupBoxSearchText.ResumeLayout(false);
            this.groupBoxSearchText.PerformLayout();
            this.groupBoxSearchOptions.ResumeLayout(false);
            this.groupBoxSearchOptions.PerformLayout();
            this.groupBoxFilters.ResumeLayout(false);
            this.groupBoxFilters.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinLevel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxLevel)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private GroupBox groupBoxSearchText;
        private TextBox textBoxSearchText;
        private Label labelSearchText;
        
        private GroupBox groupBoxSearchOptions;
        private CheckBox checkBoxSearchInDescription;
        private CheckBox checkBoxSearchInName;
        private CheckBox checkBoxSearchInId;
        
        private GroupBox groupBoxFilters;
        private Label labelQuality;
        private ComboBox comboBoxQuality;
        private Label labelWeaponType;
        private ComboBox comboBoxWeaponType;
        private Label labelItemType;
        private ComboBox comboBoxItemType;
        private Label labelMinLevel;
        private NumericUpDown numericUpDownMinLevel;
        private Label labelMaxLevel;
        private NumericUpDown numericUpDownMaxLevel;
        
        private Button buttonSearch;
        private Button buttonCancel;
        private Button buttonReset;
    }
}
