namespace GameItemManager.Models
{
    /// <summary>
    /// 字符串资源模型
    /// </summary>
    public class StringResource
    {
        /// <summary>
        /// 字符串ID
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// 字符串名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 字符串内容（中文文本）
        /// </summary>
        public string Body { get; set; } = string.Empty;
        
        /// <summary>
        /// 来源文件
        /// </summary>
        public string SourceFile { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// 字符串资源管理器
    /// </summary>
    public class StringResourceManager
    {
        private readonly Dictionary<string, StringResource> _stringResources;
        
        public StringResourceManager()
        {
            _stringResources = new Dictionary<string, StringResource>();
        }
        
        /// <summary>
        /// 添加字符串资源
        /// </summary>
        public void AddStringResource(StringResource resource)
        {
            if (!string.IsNullOrEmpty(resource.Name))
            {
                _stringResources[resource.Name] = resource;
            }
        }
        
        /// <summary>
        /// 根据字符串名称获取中文文本
        /// </summary>
        public string GetText(string stringName)
        {
            if (string.IsNullOrEmpty(stringName))
                return string.Empty;
                
            return _stringResources.TryGetValue(stringName, out var resource) 
                ? resource.Body 
                : stringName;
        }
        
        /// <summary>
        /// 检查是否包含指定的字符串名称
        /// </summary>
        public bool ContainsString(string stringName)
        {
            return !string.IsNullOrEmpty(stringName) && _stringResources.ContainsKey(stringName);
        }
        
        /// <summary>
        /// 获取所有字符串资源
        /// </summary>
        public IEnumerable<StringResource> GetAllResources()
        {
            return _stringResources.Values;
        }
        
        /// <summary>
        /// 清空所有字符串资源
        /// </summary>
        public void Clear()
        {
            _stringResources.Clear();
        }
        
        /// <summary>
        /// 获取字符串资源数量
        /// </summary>
        public int Count => _stringResources.Count;
    }
}
