using GameItemManager.Models;

namespace GameItemManager.Forms
{
    /// <summary>
    /// 物品编辑窗体
    /// </summary>
    public partial class ItemEditForm : Form
    {
        private GameItem _item;
        private readonly StringResourceManager _stringResourceManager;
        private bool _isModified = false;
        
        public GameItem Item => _item;
        public bool IsModified => _isModified;
        
        public ItemEditForm(GameItem item, StringResourceManager stringResourceManager)
        {
            InitializeComponent();
            _item = item ?? throw new ArgumentNullException(nameof(item));
            _stringResourceManager = stringResourceManager ?? throw new ArgumentNullException(nameof(stringResourceManager));
            
            InitializeUI();
            LoadItemData();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            this.Text = $"编辑物品 - {_item.DisplayName}";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(600, 400);
            
            // 设置窗体图标
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
        
        /// <summary>
        /// 加载物品数据到控件
        /// </summary>
        private void LoadItemData()
        {
            // 基本信息
            textBoxId.Text = _item.Id;
            textBoxName.Text = _item.Name;
            textBoxDesc.Text = _item.Desc;
            textBoxDescText.Text = _item.DescText;
            
            // 物品类型
            comboBoxWeaponType.Text = _item.WeaponType;
            comboBoxItemType.Text = _item.ItemType;
            comboBoxAttackType.Text = _item.AttackType;
            comboBoxQuality.Text = _item.Quality;
            
            // 外观相关
            textBoxMesh.Text = _item.Mesh;
            textBoxMaterial.Text = _item.Material;
            textBoxIconName.Text = _item.IconName;
            textBoxUiSoundType.Text = _item.UiSoundType;
            
            // 数值属性
            numericUpDownLevel.Value = _item.Level;
            numericUpDownPrice.Value = _item.Price;
            numericUpDownMaxStackCount.Value = _item.MaxStackCount;
            numericUpDownMinDamage.Value = _item.MinDamage;
            numericUpDownMaxDamage.Value = _item.MaxDamage;
            
            // 基础属性
            numericUpDownStr.Value = _item.Str;
            numericUpDownAgi.Value = _item.Agi;
            numericUpDownKno.Value = _item.Kno;
            numericUpDownVit.Value = _item.Vit;
            numericUpDownDex.Value = _item.Dex;
            numericUpDownWil.Value = _item.Wil;
            
            // 战斗属性
            numericUpDownHitAccuracy.Value = _item.HitAccuracy;
            numericUpDownCritical.Value = _item.Critical;
            numericUpDownParry.Value = _item.Parry;
            
            // 攻击相关
            numericUpDownAttackDelay.Value = _item.AttackDelay;
            numericUpDownHitCount.Value = _item.HitCount;
            numericUpDownAttackGap.Value = (decimal)_item.AttackGap;
            numericUpDownAttackRange.Value = (decimal)_item.AttackRange;
            
            // 布尔属性
            checkBoxLore.Checked = _item.Lore;
            checkBoxCanExchange.Checked = _item.CanExchange;
            checkBoxCanSellToNpc.Checked = _item.CanSellToNpc;
            checkBoxBreakable.Checked = _item.Breakable;
            checkBoxSoulBind.Checked = _item.SoulBind;
            checkBoxNoEnchant.Checked = _item.NoEnchant;
            checkBoxCanPolish.Checked = _item.CanPolish;
            
            // 职业限制
            checkBoxWarrior.Checked = _item.Warrior == 1;
            checkBoxScout.Checked = _item.Scout == 1;
            checkBoxMage.Checked = _item.Mage == 1;
            checkBoxCleric.Checked = _item.Cleric == 1;
            checkBoxEngineer.Checked = _item.Engineer == 1;
            checkBoxArtist.Checked = _item.Artist == 1;
            checkBoxFighter.Checked = _item.Fighter == 1;
            checkBoxKnight.Checked = _item.Knight == 1;
            checkBoxAssassin.Checked = _item.Assassin == 1;
            checkBoxRanger.Checked = _item.Ranger == 1;
            checkBoxWizard.Checked = _item.Wizard == 1;
            checkBoxElementalist.Checked = _item.Elementalist == 1;
            checkBoxChanter.Checked = _item.Chanter == 1;
            checkBoxPriest.Checked = _item.Priest == 1;
            checkBoxGunner.Checked = _item.Gunner == 1;
            checkBoxBard.Checked = _item.Bard == 1;
            checkBoxRider.Checked = _item.Rider == 1;
            
            // 其他属性
            textBoxEquipmentSlots.Text = _item.EquipmentSlots;
            textBoxGenderPermitted.Text = _item.GenderPermitted;
            textBoxRacePermitted.Text = _item.RacePermitted;
            textBoxEquipBone.Text = _item.EquipBone;
            textBoxBonusApply.Text = _item.BonusApply;
            textBoxTrailTex.Text = _item.TrailTex;
            
            // 附魔相关
            numericUpDownMaxEnchantValue.Value = _item.MaxEnchantValue;
            numericUpDownOptionSlotValue.Value = _item.OptionSlotValue;
            numericUpDownSpecialSlotValue.Value = _item.SpecialSlotValue;
            numericUpDownOptionSlotBonus.Value = _item.OptionSlotBonus;
            
            // 特效相关
            numericUpDownMeshChange.Value = _item.MeshChange;
            numericUpDownDmgDecal.Value = _item.DmgDecal;
            numericUpDownBladeFx.Value = _item.BladeFx;
            
            // 抛光相关
            numericUpDownPolishBurnOnAttack.Value = _item.PolishBurnOnAttack;
            numericUpDownPolishBurnOnDefend.Value = _item.PolishBurnOnDefend;
            
            // 其他
            numericUpDownCannotChangeskin.Value = _item.CannotChangeskin;
            numericUpDownCashItem.Value = _item.CashItem;
            numericUpDownBmRestrictCategory.Value = _item.BmRestrictCategory;
        }
        
        /// <summary>
        /// 验证输入数据
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(textBoxId.Text))
            {
                MessageBox.Show("物品ID不能为空", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBoxId.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                MessageBox.Show("物品名称不能为空", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBoxName.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存数据到物品对象
        /// </summary>
        private void SaveItemData()
        {
            if (!ValidateInput())
                return;
            // 基本信息
            _item.Id = textBoxId.Text.Trim();
            _item.Name = textBoxName.Text.Trim();
            _item.Desc = textBoxDesc.Text.Trim();
            _item.DescText = textBoxDescText.Text.Trim();
            
            // 物品类型
            _item.WeaponType = comboBoxWeaponType.Text.Trim();
            _item.ItemType = comboBoxItemType.Text.Trim();
            _item.AttackType = comboBoxAttackType.Text.Trim();
            _item.Quality = comboBoxQuality.Text.Trim();
            
            // 外观相关
            _item.Mesh = textBoxMesh.Text.Trim();
            _item.Material = textBoxMaterial.Text.Trim();
            _item.IconName = textBoxIconName.Text.Trim();
            _item.UiSoundType = textBoxUiSoundType.Text.Trim();
            
            // 数值属性
            _item.Level = (int)numericUpDownLevel.Value;
            _item.Price = (int)numericUpDownPrice.Value;
            _item.MaxStackCount = (int)numericUpDownMaxStackCount.Value;
            _item.MinDamage = (int)numericUpDownMinDamage.Value;
            _item.MaxDamage = (int)numericUpDownMaxDamage.Value;
            
            // 基础属性
            _item.Str = (int)numericUpDownStr.Value;
            _item.Agi = (int)numericUpDownAgi.Value;
            _item.Kno = (int)numericUpDownKno.Value;
            _item.Vit = (int)numericUpDownVit.Value;
            _item.Dex = (int)numericUpDownDex.Value;
            _item.Wil = (int)numericUpDownWil.Value;
            
            // 战斗属性
            _item.HitAccuracy = (int)numericUpDownHitAccuracy.Value;
            _item.Critical = (int)numericUpDownCritical.Value;
            _item.Parry = (int)numericUpDownParry.Value;
            
            // 攻击相关
            _item.AttackDelay = (int)numericUpDownAttackDelay.Value;
            _item.HitCount = (int)numericUpDownHitCount.Value;
            _item.AttackGap = (float)numericUpDownAttackGap.Value;
            _item.AttackRange = (float)numericUpDownAttackRange.Value;
            
            // 布尔属性
            _item.Lore = checkBoxLore.Checked;
            _item.CanExchange = checkBoxCanExchange.Checked;
            _item.CanSellToNpc = checkBoxCanSellToNpc.Checked;
            _item.Breakable = checkBoxBreakable.Checked;
            _item.SoulBind = checkBoxSoulBind.Checked;
            _item.NoEnchant = checkBoxNoEnchant.Checked;
            _item.CanPolish = checkBoxCanPolish.Checked;
            
            // 职业限制
            _item.Warrior = checkBoxWarrior.Checked ? 1 : 0;
            _item.Scout = checkBoxScout.Checked ? 1 : 0;
            _item.Mage = checkBoxMage.Checked ? 1 : 0;
            _item.Cleric = checkBoxCleric.Checked ? 1 : 0;
            _item.Engineer = checkBoxEngineer.Checked ? 1 : 0;
            _item.Artist = checkBoxArtist.Checked ? 1 : 0;
            _item.Fighter = checkBoxFighter.Checked ? 1 : 0;
            _item.Knight = checkBoxKnight.Checked ? 1 : 0;
            _item.Assassin = checkBoxAssassin.Checked ? 1 : 0;
            _item.Ranger = checkBoxRanger.Checked ? 1 : 0;
            _item.Wizard = checkBoxWizard.Checked ? 1 : 0;
            _item.Elementalist = checkBoxElementalist.Checked ? 1 : 0;
            _item.Chanter = checkBoxChanter.Checked ? 1 : 0;
            _item.Priest = checkBoxPriest.Checked ? 1 : 0;
            _item.Gunner = checkBoxGunner.Checked ? 1 : 0;
            _item.Bard = checkBoxBard.Checked ? 1 : 0;
            _item.Rider = checkBoxRider.Checked ? 1 : 0;
            
            // 其他属性
            _item.EquipmentSlots = textBoxEquipmentSlots.Text.Trim();
            _item.GenderPermitted = textBoxGenderPermitted.Text.Trim();
            _item.RacePermitted = textBoxRacePermitted.Text.Trim();
            _item.EquipBone = textBoxEquipBone.Text.Trim();
            _item.BonusApply = textBoxBonusApply.Text.Trim();
            _item.TrailTex = textBoxTrailTex.Text.Trim();
            
            // 附魔相关
            _item.MaxEnchantValue = (int)numericUpDownMaxEnchantValue.Value;
            _item.OptionSlotValue = (int)numericUpDownOptionSlotValue.Value;
            _item.SpecialSlotValue = (int)numericUpDownSpecialSlotValue.Value;
            _item.OptionSlotBonus = (int)numericUpDownOptionSlotBonus.Value;
            
            // 特效相关
            _item.MeshChange = (int)numericUpDownMeshChange.Value;
            _item.DmgDecal = (int)numericUpDownDmgDecal.Value;
            _item.BladeFx = (int)numericUpDownBladeFx.Value;
            
            // 抛光相关
            _item.PolishBurnOnAttack = (int)numericUpDownPolishBurnOnAttack.Value;
            _item.PolishBurnOnDefend = (int)numericUpDownPolishBurnOnDefend.Value;
            
            // 其他
            _item.CannotChangeskin = (int)numericUpDownCannotChangeskin.Value;
            _item.CashItem = (int)numericUpDownCashItem.Value;
            _item.BmRestrictCategory = (int)numericUpDownBmRestrictCategory.Value;
            
            _isModified = true;
        }

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void ButtonOK_Click(object sender, EventArgs e)
        {
            SaveItemData();
            if (_isModified)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void ButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void ButtonApply_Click(object sender, EventArgs e)
        {
            SaveItemData();
            if (_isModified)
            {
                MessageBox.Show("更改已应用", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// 描述字符串ID文本改变事件
        /// </summary>
        private void TextBoxDesc_TextChanged(object sender, EventArgs e)
        {
            // 当描述字符串ID改变时，自动更新中文文本
            var descId = textBoxDesc.Text.Trim();
            if (!string.IsNullOrEmpty(descId))
            {
                var chineseText = _stringResourceManager.GetText(descId);
                if (!string.IsNullOrEmpty(chineseText) && chineseText != descId)
                {
                    textBoxDescText.Text = chineseText;
                }
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ItemEditForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 检查是否有未保存的更改
            if (_isModified && this.DialogResult != DialogResult.OK)
            {
                var result = MessageBox.Show("有未保存的更改，是否要保存？", "确认",
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    SaveItemData();
                    this.DialogResult = DialogResult.OK;
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }

        #endregion
    }
}
