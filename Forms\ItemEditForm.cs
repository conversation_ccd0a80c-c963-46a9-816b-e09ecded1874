using GameItemManager.Models;

namespace GameItemManager.Forms
{
    /// <summary>
    /// 物品编辑窗体
    /// </summary>
    public partial class ItemEditForm : Form
    {
        private GameItem _item;
        private readonly StringResourceManager _stringResourceManager;
        private bool _isModified = false;
        
        public GameItem Item => _item;
        public bool IsModified => _isModified;
        
        public ItemEditForm(GameItem item, StringResourceManager stringResourceManager)
        {
            InitializeComponent();
            _item = item ?? throw new ArgumentNullException(nameof(item));
            _stringResourceManager = stringResourceManager ?? throw new ArgumentNullException(nameof(stringResourceManager));
            
            InitializeUI();
            LoadItemData();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            this.Text = $"编辑物品 - {_item.DisplayName}";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(600, 400);
            
            // 设置窗体图标
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
        
        /// <summary>
        /// 加载物品数据到控件
        /// </summary>
        private void LoadItemData()
        {
            // 将物品对象绑定到PropertyGrid
            propertyGrid1.SelectedObject = _item;
        }
        
        /// <summary>
        /// 验证输入数据
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(_item.Id))
            {
                MessageBox.Show("物品ID不能为空", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(_item.Name))
            {
                MessageBox.Show("物品名称不能为空", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存数据到物品对象
        /// </summary>
        private void SaveItemData()
        {
            // PropertyGrid会自动更新绑定的对象，所以这里只需要验证
            if (ValidateInput())
            {
                _isModified = true;
        }
        }

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void ButtonOK_Click(object sender, EventArgs e)
        {
            SaveItemData();
            if (_isModified)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void ButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void ButtonApply_Click(object sender, EventArgs e)
        {
            SaveItemData();
            if (_isModified)
            {
                MessageBox.Show("更改已应用", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// PropertyGrid属性值改变事件
        /// </summary>
        private void PropertyGrid1_PropertyValueChanged(object s, PropertyValueChangedEventArgs e)
        {
            _isModified = true;

            // 如果修改的是描述字符串ID，自动更新中文文本
            if (e.ChangedItem?.PropertyDescriptor?.Name == "Desc")
            {
                var descId = _item.Desc?.Trim();
                if (!string.IsNullOrEmpty(descId))
                {
                    var chineseText = _stringResourceManager.GetText(descId);
                    if (!string.IsNullOrEmpty(chineseText) && chineseText != descId)
                    {
                        _item.DescText = chineseText;
                        propertyGrid1.Refresh();
                    }
                }
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ItemEditForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 检查是否有未保存的更改
            if (_isModified && this.DialogResult != DialogResult.OK)
            {
                var result = MessageBox.Show("有未保存的更改，是否要保存？", "确认",
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    SaveItemData();
                    this.DialogResult = DialogResult.OK;
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }

        #endregion
    }
}
