using GameItemManager.Models;
using GameItemManager.Services;

namespace GameItemManager
{
    /// <summary>
    /// 主窗体
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly XmlFileService _xmlFileService;
        private readonly StringResourceManager _stringResourceManager;
        private List<GameItem> _allItems;
        private List<GameItem> _filteredItems;
        
        public MainForm()
        {
            InitializeComponent();
            
            _xmlFileService = new XmlFileService();
            _stringResourceManager = new StringResourceManager();
            _allItems = new List<GameItem>();
            _filteredItems = new List<GameItem>();
            
            InitializeUI();
            LoadStringResources();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            this.Text = "游戏物品管理器 v1.0";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);
            
            // 设置状态栏初始信息
            toolStripStatusLabel1.Text = "就绪";
            toolStripStatusLabel2.Text = "物品数量: 0";
        }
        
        /// <summary>
        /// 加载字符串资源
        /// </summary>
        private async void LoadStringResources()
        {
            try
            {
                toolStripStatusLabel1.Text = "正在加载字符串资源...";
                toolStripProgressBar1.Visible = true;
                
                await Task.Run(() =>
                {
                    _xmlFileService.LoadStringResources(_stringResourceManager);
                });
                
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = $"字符串资源加载完成，共 {_stringResourceManager.Count} 条";
            }
            catch (Exception ex)
            {
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "字符串资源加载失败";
                MessageBox.Show($"加载字符串资源时发生错误：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 加载XML文件
        /// </summary>
        private async void LoadXmlFiles()
        {
            try
            {
                toolStripStatusLabel1.Text = "正在加载XML文件...";
                toolStripProgressBar1.Visible = true;
                
                var items = await Task.Run(() =>
                {
                    return _xmlFileService.LoadAllGameItems(_stringResourceManager);
                });
                
                _allItems = items;
                _filteredItems = new List<GameItem>(_allItems);
                
                UpdateItemList();
                
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "XML文件加载完成";
                toolStripStatusLabel2.Text = $"物品数量: {_allItems.Count}";
            }
            catch (Exception ex)
            {
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "XML文件加载失败";
                MessageBox.Show($"加载XML文件时发生错误：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 更新物品列表显示
        /// </summary>
        private void UpdateItemList()
        {
            listViewItems.Items.Clear();
            
            foreach (var item in _filteredItems)
            {
                var listItem = new ListViewItem(item.Id);
                listItem.SubItems.Add(item.Name);
                listItem.SubItems.Add(item.DisplayName);
                listItem.SubItems.Add(item.WeaponType);
                listItem.SubItems.Add(item.Quality);
                listItem.SubItems.Add(item.Level.ToString());
                listItem.Tag = item;
                
                listViewItems.Items.Add(listItem);
            }
            
            toolStripStatusLabel2.Text = $"显示物品: {_filteredItems.Count} / {_allItems.Count}";
        }
        
        /// <summary>
        /// 搜索物品
        /// </summary>
        private void SearchItems(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredItems = new List<GameItem>(_allItems);
            }
            else
            {
                searchText = searchText.ToLower();
                _filteredItems = _allItems.Where(item =>
                    item.Id.ToLower().Contains(searchText) ||
                    item.Name.ToLower().Contains(searchText) ||
                    item.DisplayName.ToLower().Contains(searchText) ||
                    item.WeaponType.ToLower().Contains(searchText)
                ).ToList();
            }
            
            UpdateItemList();
        }
        
        /// <summary>
        /// 显示物品详细信息
        /// </summary>
        private void ShowItemDetails(GameItem item)
        {
            if (item == null)
            {
                propertyGridItem.SelectedObject = null;
                return;
            }
            
            propertyGridItem.SelectedObject = item;
        }
        
        #region 事件处理方法

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时自动加载XML文件
            // LoadXmlFiles(); // 可以选择是否自动加载
        }

        /// <summary>
        /// 加载XML文件菜单点击事件
        /// </summary>
        private void LoadXmlFilesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LoadXmlFiles();
        }

        /// <summary>
        /// 保存全部菜单点击事件
        /// </summary>
        private void SaveAllToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // TODO: 实现保存功能
            MessageBox.Show("保存功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 退出菜单点击事件
        /// </summary>
        private void ExitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 新建物品菜单点击事件
        /// </summary>
        private void AddNewItemToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // TODO: 实现新建物品功能
            MessageBox.Show("新建物品功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除物品菜单点击事件
        /// </summary>
        private void DeleteItemToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
            if (selectedItem != null)
            {
                var result = MessageBox.Show($"确定要删除物品 [{selectedItem.Id}] {selectedItem.DisplayName} 吗？",
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // TODO: 实现删除功能
                    MessageBox.Show("删除功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        /// <summary>
        /// 查找菜单点击事件
        /// </summary>
        private void FindToolStripMenuItem_Click(object sender, EventArgs e)
        {
            toolStripTextBoxSearch.Focus();
        }

        /// <summary>
        /// 关于菜单点击事件
        /// </summary>
        private void AboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("游戏物品管理器 v1.0\n\n用于管理XML游戏物品数据的工具\n\n开发者：AI助手",
                "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑物品按钮点击事件
        /// </summary>
        private void EditItemButton_Click(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要编辑的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
            if (selectedItem != null)
            {
                // TODO: 打开编辑窗体
                MessageBox.Show("编辑功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// 搜索文本框按键事件
        /// </summary>
        private void ToolStripTextBoxSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                SearchItems(toolStripTextBoxSearch.Text);
                e.Handled = true;
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void ToolStripButtonSearch_Click(object sender, EventArgs e)
        {
            SearchItems(toolStripTextBoxSearch.Text);
        }

        /// <summary>
        /// 清空搜索按钮点击事件
        /// </summary>
        private void ToolStripButtonClearSearch_Click(object sender, EventArgs e)
        {
            toolStripTextBoxSearch.Text = string.Empty;
            SearchItems(string.Empty);
        }

        /// <summary>
        /// 文件列表选择改变事件
        /// </summary>
        private void ListViewFiles_SelectedIndexChanged(object sender, EventArgs e)
        {
            // TODO: 根据选择的文件过滤物品列表
        }

        /// <summary>
        /// 物品列表选择改变事件
        /// </summary>
        private void ListViewItems_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count > 0)
            {
                var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
                ShowItemDetails(selectedItem);
            }
            else
            {
                ShowItemDetails(null);
            }
        }

        /// <summary>
        /// 物品列表双击事件
        /// </summary>
        private void ListViewItems_DoubleClick(object sender, EventArgs e)
        {
            EditItemButton_Click(sender, e);
        }

        /// <summary>
        /// 属性网格值改变事件
        /// </summary>
        private void PropertyGridItem_PropertyValueChanged(object s, PropertyValueChangedEventArgs e)
        {
            // 当属性值改变时，更新列表显示
            if (listViewItems.SelectedItems.Count > 0)
            {
                var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
                if (selectedItem != null)
                {
                    var listItem = listViewItems.SelectedItems[0];
                    listItem.SubItems[0].Text = selectedItem.Id;
                    listItem.SubItems[1].Text = selectedItem.Name;
                    listItem.SubItems[2].Text = selectedItem.DisplayName;
                    listItem.SubItems[3].Text = selectedItem.WeaponType;
                    listItem.SubItems[4].Text = selectedItem.Quality;
                    listItem.SubItems[5].Text = selectedItem.Level.ToString();
                }
            }
        }

        #endregion
    }
}
