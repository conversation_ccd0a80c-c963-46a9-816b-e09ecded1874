using GameItemManager.Models;
using GameItemManager.Services;
using GameItemManager.Utils;

namespace GameItemManager
{
    /// <summary>
    /// 主窗体
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly XmlFileService _xmlFileService;
        private readonly StringResourceManager _stringResourceManager;
        private List<GameItem> _allItems;
        private List<GameItem> _filteredItems;
        private readonly string _workingDirectory;
        
        public MainForm()
        {
            InitializeComponent();

            _xmlFileService = new XmlFileService();
            _stringResourceManager = new StringResourceManager();
            _allItems = new List<GameItem>();
            _filteredItems = new List<GameItem>();
            _workingDirectory = Directory.GetCurrentDirectory();

            InitializeUI();
            LoadStringResources();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            this.Text = "游戏物品管理器 v1.0";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);

            // 设置状态栏初始信息
            toolStripStatusLabel1.Text = "就绪 - 请点击'加载XML文件'开始";
            toolStripStatusLabel2.Text = "物品数量: 0";

            // 添加工具提示
            SetupToolTips();

            // 设置列表视图的排序
            SetupListViewSorting();
        }

        /// <summary>
        /// 设置工具提示
        /// </summary>
        private void SetupToolTips()
        {
            // 为ToolStrip项目设置ToolTipText属性
            toolStripButtonLoad.ToolTipText = "加载XML文件 (Ctrl+O)";
            toolStripButtonSave.ToolTipText = "保存所有修改 (Ctrl+S)";
            toolStripButtonAdd.ToolTipText = "新建物品 (Ctrl+N)";
            toolStripButtonEdit.ToolTipText = "编辑选中的物品";
            toolStripButtonDelete.ToolTipText = "删除选中的物品 (Delete)";
            toolStripTextBoxSearch.ToolTipText = "输入搜索关键词，支持ID、名称、描述等";
            toolStripButtonSearch.ToolTipText = "执行搜索";
            toolStripButtonClearSearch.ToolTipText = "清空搜索条件";
        }

        /// <summary>
        /// 设置列表视图排序
        /// </summary>
        private void SetupListViewSorting()
        {
            // 为物品列表添加排序功能
            listViewItems.ListViewItemSorter = new ListViewItemComparer(0);
            listViewItems.ColumnClick += ListViewItems_ColumnClick;
        }
        
        /// <summary>
        /// 加载字符串资源
        /// </summary>
        private async void LoadStringResources()
        {
            try
            {
                toolStripStatusLabel1.Text = "正在加载字符串资源...";
                toolStripProgressBar1.Visible = true;
                
                await Task.Run(() =>
                {
                    _xmlFileService.LoadStringResources(_stringResourceManager);
                });
                
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = $"字符串资源加载完成，共 {_stringResourceManager.Count} 条";
            }
            catch (Exception ex)
            {
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "字符串资源加载失败";
                ShowError($"加载字符串资源时发生错误：{ex.Message}\n\n请检查strings文件夹是否存在且包含有效的XML文件。");
            }
        }
        
        /// <summary>
        /// 加载XML文件
        /// </summary>
        private async void LoadXmlFiles()
        {
            try
            {
                toolStripStatusLabel1.Text = "正在加载XML文件...";
                toolStripProgressBar1.Visible = true;
                
                var items = await Task.Run(() =>
                {
                    return _xmlFileService.LoadAllGameItems(_stringResourceManager);
                });
                
                _allItems = items;
                _filteredItems = new List<GameItem>(_allItems);
                
                UpdateItemList();
                
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "XML文件加载完成";
                toolStripStatusLabel2.Text = $"物品数量: {_allItems.Count}";
            }
            catch (Exception ex)
            {
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "XML文件加载失败";
                ShowError($"加载XML文件时发生错误：{ex.Message}\n\n请确保当前目录包含有效的client_items*.xml文件。");
            }
        }
        
        /// <summary>
        /// 更新物品列表显示
        /// </summary>
        private void UpdateItemList()
        {
            listViewItems.Items.Clear();
            
            foreach (var item in _filteredItems)
            {
                var listItem = new ListViewItem(item.Id);
                listItem.SubItems.Add(item.Name);
                listItem.SubItems.Add(item.DisplayName);
                listItem.SubItems.Add(item.WeaponType);
                listItem.SubItems.Add(item.Quality);
                listItem.SubItems.Add(item.Level.ToString());
                listItem.Tag = item;
                
                listViewItems.Items.Add(listItem);
            }
            
            toolStripStatusLabel2.Text = $"显示物品: {_filteredItems.Count} / {_allItems.Count}";
        }
        
        /// <summary>
        /// 搜索物品
        /// </summary>
        private void SearchItems(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredItems = new List<GameItem>(_allItems);
            }
            else
            {
                searchText = searchText.ToLower();
                _filteredItems = _allItems.Where(item =>
                    item.Id.ToLower().Contains(searchText) ||
                    item.Name.ToLower().Contains(searchText) ||
                    item.DisplayName.ToLower().Contains(searchText) ||
                    item.WeaponType.ToLower().Contains(searchText) ||
                    item.ItemType.ToLower().Contains(searchText) ||
                    item.Quality.ToLower().Contains(searchText) ||
                    item.AttackType.ToLower().Contains(searchText) ||
                    item.Desc.ToLower().Contains(searchText) ||
                    item.DescText.ToLower().Contains(searchText) ||
                    item.Level.ToString().Contains(searchText)
                ).ToList();
            }

            UpdateItemList();
        }

        /// <summary>
        /// 按品质过滤物品
        /// </summary>
        private void FilterByQuality(string quality)
        {
            if (string.IsNullOrWhiteSpace(quality) || quality == "全部")
            {
                _filteredItems = new List<GameItem>(_allItems);
            }
            else
            {
                _filteredItems = _allItems.Where(item =>
                    item.Quality.Equals(quality, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            UpdateItemList();
        }

        /// <summary>
        /// 按武器类型过滤物品
        /// </summary>
        private void FilterByWeaponType(string weaponType)
        {
            if (string.IsNullOrWhiteSpace(weaponType) || weaponType == "全部")
            {
                _filteredItems = new List<GameItem>(_allItems);
            }
            else
            {
                _filteredItems = _allItems.Where(item =>
                    item.WeaponType.Equals(weaponType, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            UpdateItemList();
        }

        /// <summary>
        /// 按等级范围过滤物品
        /// </summary>
        private void FilterByLevelRange(int minLevel, int maxLevel)
        {
            _filteredItems = _allItems.Where(item =>
                item.Level >= minLevel && item.Level <= maxLevel
            ).ToList();

            UpdateItemList();
        }
        
        /// <summary>
        /// 显示物品详细信息
        /// </summary>
        private void ShowItemDetails(GameItem item)
        {
            if (item == null)
            {
                propertyGridItem.SelectedObject = null;
                return;
            }
            
            propertyGridItem.SelectedObject = item;
        }
        
        #region 事件处理方法

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时自动加载XML文件
            // LoadXmlFiles(); // 可以选择是否自动加载
        }

        /// <summary>
        /// 加载XML文件菜单点击事件
        /// </summary>
        private void LoadXmlFilesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LoadXmlFiles();
        }

        /// <summary>
        /// 保存全部菜单点击事件
        /// </summary>
        private void SaveAllToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveAllItems();
        }

        /// <summary>
        /// 保存所有物品到XML文件
        /// </summary>
        private async void SaveAllItems()
        {
            if (_allItems == null || _allItems.Count == 0)
            {
                ShowInfo("没有可保存的物品数据，请先加载XML文件。");
                return;
            }

            if (!ShowConfirm("确定要保存所有修改吗？\n\n这将覆盖原有的XML文件，建议先备份重要数据。\n\n系统会自动创建备份文件。"))
                return;

            try
            {
                toolStripStatusLabel1.Text = "正在保存XML文件...";
                toolStripProgressBar1.Visible = true;

                await Task.Run(() =>
                {
                    // 按文件分组保存物品
                    var itemGroups = GroupItemsByFile(_allItems);

                    foreach (var group in itemGroups)
                    {
                        var filePath = group.Key;
                        var items = group.Value;

                        // 创建备份
                        CreateBackup(filePath);

                        // 保存文件
                        _xmlFileService.SaveGameItemsToFile(filePath, items);
                    }
                });

                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = $"保存完成 - 共保存 {_allItems.Count} 个物品";

                ShowInfo($"保存成功！\n\n共保存了 {_allItems.Count} 个物品到XML文件。\n备份文件已自动创建。");
            }
            catch (Exception ex)
            {
                toolStripProgressBar1.Visible = false;
                toolStripStatusLabel1.Text = "保存失败";

                ShowError($"保存文件时发生错误：{ex.Message}\n\n请检查文件是否被其他程序占用，或者是否有足够的磁盘空间。");
            }
        }

        /// <summary>
        /// 按文件分组物品
        /// </summary>
        private Dictionary<string, List<GameItem>> GroupItemsByFile(List<GameItem> items)
        {
            var groups = new Dictionary<string, List<GameItem>>();

            // 简单的分组逻辑：按武器类型分组到不同文件
            foreach (var item in items)
            {
                string fileName;

                if (!string.IsNullOrEmpty(item.WeaponType))
                {
                    fileName = Path.Combine(_workingDirectory, "client_items_armor.xml");
                }
                else
                {
                    fileName = Path.Combine(_workingDirectory, "client_items_etc.xml");
                }

                if (!groups.ContainsKey(fileName))
                {
                    groups[fileName] = new List<GameItem>();
                }

                groups[fileName].Add(item);
            }

            return groups;
        }

        /// <summary>
        /// 创建文件备份
        /// </summary>
        private void CreateBackup(string filePath)
        {
            if (File.Exists(filePath))
            {
                var backupPath = filePath + ".backup." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                File.Copy(filePath, backupPath, true);
            }
        }

        /// <summary>
        /// 退出菜单点击事件
        /// </summary>
        private void ExitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 新建物品菜单点击事件
        /// </summary>
        private void AddNewItemToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // TODO: 实现新建物品功能
            MessageBox.Show("新建物品功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除物品菜单点击事件
        /// </summary>
        private void DeleteItemToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
            if (selectedItem != null)
            {
                var result = MessageBox.Show($"确定要删除物品 [{selectedItem.Id}] {selectedItem.DisplayName} 吗？",
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // TODO: 实现删除功能
                    MessageBox.Show("删除功能将在后续版本中实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        /// <summary>
        /// 查找菜单点击事件
        /// </summary>
        private void FindToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowAdvancedSearch();
        }

        /// <summary>
        /// 显示高级搜索窗体
        /// </summary>
        private void ShowAdvancedSearch()
        {
            using (var searchForm = new Forms.AdvancedSearchForm())
            {
                if (searchForm.ShowDialog() == DialogResult.OK)
                {
                    // 应用搜索条件
                    _filteredItems = searchForm.FilterItems(_allItems);
                    UpdateItemList();

                    toolStripStatusLabel1.Text = $"搜索完成，找到 {_filteredItems.Count} 个匹配项";
                }
            }
        }

        /// <summary>
        /// 关于菜单点击事件
        /// </summary>
        private void AboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("游戏物品管理器 v1.0\n\n用于管理XML游戏物品数据的工具\n\n开发者：AI助手",
                "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑物品按钮点击事件
        /// </summary>
        private void EditItemButton_Click(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要编辑的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
            if (selectedItem != null)
            {
                using (var editForm = new Forms.ItemEditForm(selectedItem, _stringResourceManager))
                {
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        // 更新列表显示
                        var listItem = listViewItems.SelectedItems[0];
                        listItem.SubItems[0].Text = selectedItem.Id;
                        listItem.SubItems[1].Text = selectedItem.Name;
                        listItem.SubItems[2].Text = selectedItem.DisplayName;
                        listItem.SubItems[3].Text = selectedItem.WeaponType;
                        listItem.SubItems[4].Text = selectedItem.Quality;
                        listItem.SubItems[5].Text = selectedItem.Level.ToString();

                        // 刷新属性网格
                        propertyGridItem.Refresh();

                        toolStripStatusLabel1.Text = "物品已更新";
                    }
                }
            }
        }

        /// <summary>
        /// 搜索文本框按键事件
        /// </summary>
        private void ToolStripTextBoxSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                SearchItems(toolStripTextBoxSearch.Text);
                e.Handled = true;
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void ToolStripButtonSearch_Click(object sender, EventArgs e)
        {
            SearchItems(toolStripTextBoxSearch.Text);
        }

        /// <summary>
        /// 清空搜索按钮点击事件
        /// </summary>
        private void ToolStripButtonClearSearch_Click(object sender, EventArgs e)
        {
            toolStripTextBoxSearch.Text = string.Empty;
            SearchItems(string.Empty);
        }

        /// <summary>
        /// 文件列表选择改变事件
        /// </summary>
        private void ListViewFiles_SelectedIndexChanged(object sender, EventArgs e)
        {
            // TODO: 根据选择的文件过滤物品列表
        }

        /// <summary>
        /// 物品列表选择改变事件
        /// </summary>
        private void ListViewItems_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewItems.SelectedItems.Count > 0)
            {
                var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
                ShowItemDetails(selectedItem);
            }
            else
            {
                ShowItemDetails(null);
            }
        }

        /// <summary>
        /// 物品列表双击事件
        /// </summary>
        private void ListViewItems_DoubleClick(object sender, EventArgs e)
        {
            EditItemButton_Click(sender, e);
        }

        /// <summary>
        /// 属性网格值改变事件
        /// </summary>
        private void PropertyGridItem_PropertyValueChanged(object s, PropertyValueChangedEventArgs e)
        {
            // 当属性值改变时，更新列表显示
            if (listViewItems.SelectedItems.Count > 0)
            {
                var selectedItem = listViewItems.SelectedItems[0].Tag as GameItem;
                if (selectedItem != null)
                {
                    var listItem = listViewItems.SelectedItems[0];
                    listItem.SubItems[0].Text = selectedItem.Id;
                    listItem.SubItems[1].Text = selectedItem.Name;
                    listItem.SubItems[2].Text = selectedItem.DisplayName;
                    listItem.SubItems[3].Text = selectedItem.WeaponType;
                    listItem.SubItems[4].Text = selectedItem.Quality;
                    listItem.SubItems[5].Text = selectedItem.Level.ToString();
                }
            }
        }

        /// <summary>
        /// 列表视图列点击事件（用于排序）
        /// </summary>
        private void ListViewItems_ColumnClick(object sender, ColumnClickEventArgs e)
        {
            var comparer = listViewItems.ListViewItemSorter as ListViewItemComparer;
            if (comparer != null)
            {
                // 如果点击的是同一列，切换排序顺序
                if (e.Column == GetCurrentSortColumn())
                {
                    var currentOrder = GetCurrentSortOrder();
                    var newOrder = currentOrder == SortOrder.Ascending ? SortOrder.Descending : SortOrder.Ascending;
                    comparer.SetSortOrder(newOrder);
                }
                else
                {
                    // 点击新列，设置为升序
                    comparer.SetColumnIndex(e.Column);
                    comparer.SetSortOrder(SortOrder.Ascending);
                }

                listViewItems.Sort();
                UpdateSortIndicator(e.Column, comparer);
            }
        }

        private int _currentSortColumn = 0;
        private SortOrder _currentSortOrder = SortOrder.Ascending;

        private int GetCurrentSortColumn() => _currentSortColumn;
        private SortOrder GetCurrentSortOrder() => _currentSortOrder;

        /// <summary>
        /// 更新排序指示器
        /// </summary>
        private void UpdateSortIndicator(int columnIndex, ListViewItemComparer comparer)
        {
            _currentSortColumn = columnIndex;
            _currentSortOrder = comparer.GetType().GetField("_sortOrder",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(comparer) as SortOrder? ?? SortOrder.Ascending;

            // 更新状态栏显示排序信息
            var columnName = listViewItems.Columns[columnIndex].Text;
            var orderText = _currentSortOrder == SortOrder.Ascending ? "升序" : "降序";
            toolStripStatusLabel1.Text = $"按 {columnName} {orderText} 排序";
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        private void ShowError(string message, string title = "错误")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        private void ShowInfo(string message, string title = "信息")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        private void ShowWarning(string message, string title = "警告")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// 确认对话框
        /// </summary>
        private bool ShowConfirm(string message, string title = "确认")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
        }

        #endregion
    }
}
