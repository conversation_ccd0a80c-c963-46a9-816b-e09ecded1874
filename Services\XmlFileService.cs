using System.Text;
using System.Xml;
using GameItemManager.Models;

namespace GameItemManager.Services
{
    /// <summary>
    /// XML文件服务类，负责读取和解析XML文件
    /// </summary>
    public class XmlFileService
    {
        private readonly string _workingDirectory;
        
        public XmlFileService()
        {
            _workingDirectory = Directory.GetCurrentDirectory();
        }
        
        /// <summary>
        /// 加载所有字符串资源
        /// </summary>
        public void LoadStringResources(StringResourceManager stringManager)
        {
            var stringsPath = Path.Combine(_workingDirectory, "strings");
            if (!Directory.Exists(stringsPath))
            {
                throw new DirectoryNotFoundException($"字符串目录不存在: {stringsPath}");
            }
            
            var xmlFiles = Directory.GetFiles(stringsPath, "*.xml");
            foreach (var xmlFile in xmlFiles)
            {
                try
                {
                    LoadStringResourceFile(xmlFile, stringManager);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他文件
                    Console.WriteLine($"加载字符串文件失败 {xmlFile}: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 加载单个字符串资源文件
        /// </summary>
        private void LoadStringResourceFile(string filePath, StringResourceManager stringManager)
        {
            var fileName = Path.GetFileName(filePath);

            // 尝试不同的编码方式读取文件
            string content = ReadXmlFileWithEncoding(filePath);

            if (string.IsNullOrEmpty(content))
                return;

            try
            {
                // 首先尝试标准XML解析
                if (TryParseAsStandardXml(content, fileName, stringManager))
                    return;

                // 如果标准XML解析失败，尝试自定义格式解析
                ParseCustomStringFormat(content, fileName, stringManager);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析字符串文件失败 {fileName}: {ex.Message}");
            }
        }

        /// <summary>
        /// 尝试按标准XML格式解析
        /// </summary>
        private bool TryParseAsStandardXml(string content, string fileName, StringResourceManager stringManager)
        {
            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(content);

                // 查找字符串节点
                var stringNodes = doc.SelectNodes("//string");
                if (stringNodes != null)
                {
                    foreach (XmlNode node in stringNodes)
                    {
                        var id = node.Attributes?["id"]?.Value;
                        var name = node.Attributes?["name"]?.Value;
                        var body = node.Attributes?["body"]?.Value ?? node.InnerText;

                        if (!string.IsNullOrEmpty(name))
                        {
                            var resource = new StringResource
                            {
                                Id = id ?? string.Empty,
                                Name = name,
                                Body = body ?? string.Empty,
                                SourceFile = fileName
                            };

                            stringManager.AddStringResource(resource);
                        }
                    }
                    return true;
                }
            }
            catch (XmlException)
            {
                // XML解析失败，返回false以尝试自定义格式
                return false;
            }

            return false;
        }

        /// <summary>
        /// 解析自定义字符串格式（基于观察到的文件格式）
        /// </summary>
        private void ParseCustomStringFormat(string content, string fileName, StringResourceManager stringManager)
        {
            // 移除所有空字符和控制字符
            content = new string(content.Where(c => !char.IsControl(c) || char.IsWhiteSpace(c)).ToArray());

            // 使用正则表达式或简单的字符串分割来解析
            var lines = content.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

            string currentId = string.Empty;
            string currentName = string.Empty;
            string currentBody = string.Empty;

            foreach (var line in lines)
            {
                var cleanLine = line.Trim();
                if (string.IsNullOrEmpty(cleanLine))
                    continue;

                // 尝试匹配ID模式 (数字)
                if (System.Text.RegularExpressions.Regex.IsMatch(cleanLine, @"^\d+$"))
                {
                    // 保存之前的条目
                    if (!string.IsNullOrEmpty(currentName))
                    {
                        SaveStringResource(currentId, currentName, currentBody, fileName, stringManager);
                    }

                    currentId = cleanLine;
                    currentName = string.Empty;
                    currentBody = string.Empty;
                }
                // 尝试匹配字符串名称模式 (STR_开头)
                else if (cleanLine.StartsWith("STR_"))
                {
                    currentName = cleanLine;
                }
                // 其他内容作为body
                else if (!string.IsNullOrEmpty(currentName))
                {
                    if (!string.IsNullOrEmpty(currentBody))
                        currentBody += " ";
                    currentBody += cleanLine;
                }
            }

            // 保存最后一个条目
            if (!string.IsNullOrEmpty(currentName))
            {
                SaveStringResource(currentId, currentName, currentBody, fileName, stringManager);
            }
        }

        /// <summary>
        /// 保存字符串资源
        /// </summary>
        private void SaveStringResource(string id, string name, string body, string fileName, StringResourceManager stringManager)
        {
            var resource = new StringResource
            {
                Id = id,
                Name = name,
                Body = body,
                SourceFile = fileName
            };

            stringManager.AddStringResource(resource);
        }
        
        /// <summary>
        /// 使用多种编码方式尝试读取XML文件
        /// </summary>
        private string ReadXmlFileWithEncoding(string filePath)
        {
            // 尝试的编码列表
            var encodings = new[]
            {
                Encoding.Unicode,      // UTF-16 LE
                Encoding.BigEndianUnicode, // UTF-16 BE
                Encoding.UTF8,
                Encoding.GetEncoding("GB2312"),
                Encoding.GetEncoding("GBK")
            };
            
            foreach (var encoding in encodings)
            {
                try
                {
                    var content = File.ReadAllText(filePath, encoding);
                    
                    // 检查是否包含XML声明或看起来像有效的XML
                    if (content.Contains("<?xml") || content.Contains("<string"))
                    {
                        // 清理可能的BOM和无效字符
                        content = CleanXmlContent(content);
                        return content;
                    }
                }
                catch
                {
                    // 继续尝试下一种编码
                }
            }
            
            return string.Empty;
        }
        
        /// <summary>
        /// 清理XML内容
        /// </summary>
        private string CleanXmlContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;
                
            // 移除BOM
            if (content.StartsWith("\uFEFF"))
                content = content.Substring(1);
                
            // 移除空字符
            content = content.Replace("\0", "");
            
            // 确保XML声明正确
            if (!content.TrimStart().StartsWith("<?xml"))
            {
                content = "<?xml version=\"1.0\" encoding=\"utf-16\"?>\n" + content;
            }
            
            return content;
        }
        
        /// <summary>
        /// 加载所有游戏物品
        /// </summary>
        public List<GameItem> LoadAllGameItems(StringResourceManager stringManager)
        {
            var items = new List<GameItem>();
            
            // 查找所有client_items开头的XML文件
            var xmlFiles = Directory.GetFiles(_workingDirectory, "client_items*.xml");
            
            foreach (var xmlFile in xmlFiles)
            {
                try
                {
                    var fileItems = LoadGameItemsFromFile(xmlFile, stringManager);
                    items.AddRange(fileItems);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载物品文件失败 {xmlFile}: {ex.Message}");
                }
            }
            
            return items;
        }
        
        /// <summary>
        /// 从单个文件加载游戏物品
        /// </summary>
        private List<GameItem> LoadGameItemsFromFile(string filePath, StringResourceManager stringManager)
        {
            var items = new List<GameItem>();
            
            string xmlContent = ReadXmlFileWithEncoding(filePath);
            if (string.IsNullOrEmpty(xmlContent))
                return items;
                
            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(xmlContent);
                
                // 查找client_item节点
                var itemNodes = doc.SelectNodes("//client_item");
                if (itemNodes != null)
                {
                    foreach (XmlNode node in itemNodes)
                    {
                        var item = ParseGameItem(node, stringManager);
                        if (item != null)
                        {
                            items.Add(item);
                        }
                    }
                }
            }
            catch (XmlException ex)
            {
                throw new Exception($"解析XML文件失败: {ex.Message}");
            }
            
            return items;
        }

        /// <summary>
        /// 解析单个游戏物品节点
        /// </summary>
        private GameItem? ParseGameItem(XmlNode itemNode, StringResourceManager stringManager)
        {
            try
            {
                var item = new GameItem();

                // 解析所有子节点
                foreach (XmlNode child in itemNode.ChildNodes)
                {
                    if (child.NodeType != XmlNodeType.Element)
                        continue;

                    var nodeName = child.Name.ToLower();
                    var nodeValue = child.InnerText?.Trim() ?? string.Empty;

                    switch (nodeName)
                    {
                        case "id":
                            item.Id = nodeValue;
                            break;
                        case "name":
                            item.Name = nodeValue;
                            break;
                        case "desc":
                            item.Desc = nodeValue;
                            item.DescText = stringManager.GetText(nodeValue);
                            break;
                        case "weapon_type":
                            item.WeaponType = nodeValue;
                            break;
                        case "item_type":
                            item.ItemType = nodeValue;
                            break;
                        case "attack_type":
                            item.AttackType = nodeValue;
                            break;
                        case "quality":
                            item.Quality = nodeValue;
                            break;
                        case "mesh":
                            item.Mesh = nodeValue;
                            break;
                        case "material":
                            item.Material = nodeValue;
                            break;
                        case "icon_name":
                            item.IconName = nodeValue;
                            break;
                        case "ui_sound_type":
                            item.UiSoundType = nodeValue;
                            break;
                        case "level":
                            int.TryParse(nodeValue, out var level);
                            item.Level = level;
                            break;
                        case "price":
                            int.TryParse(nodeValue, out var price);
                            item.Price = price;
                            break;
                        case "max_stack_count":
                            int.TryParse(nodeValue, out var maxStack);
                            item.MaxStackCount = maxStack;
                            break;
                        case "min_damage":
                            int.TryParse(nodeValue, out var minDmg);
                            item.MinDamage = minDmg;
                            break;
                        case "max_damage":
                            int.TryParse(nodeValue, out var maxDmg);
                            item.MaxDamage = maxDmg;
                            break;
                        case "str":
                            int.TryParse(nodeValue, out var str);
                            item.Str = str;
                            break;
                        case "agi":
                            int.TryParse(nodeValue, out var agi);
                            item.Agi = agi;
                            break;
                        case "kno":
                            int.TryParse(nodeValue, out var kno);
                            item.Kno = kno;
                            break;
                        case "vit":
                            int.TryParse(nodeValue, out var vit);
                            item.Vit = vit;
                            break;
                        case "dex":
                            int.TryParse(nodeValue, out var dex);
                            item.Dex = dex;
                            break;
                        case "wil":
                            int.TryParse(nodeValue, out var wil);
                            item.Wil = wil;
                            break;
                        case "hit_accuracy":
                            int.TryParse(nodeValue, out var hitAcc);
                            item.HitAccuracy = hitAcc;
                            break;
                        case "critical":
                            int.TryParse(nodeValue, out var crit);
                            item.Critical = crit;
                            break;
                        case "parry":
                            int.TryParse(nodeValue, out var parry);
                            item.Parry = parry;
                            break;
                        case "magical_skill_boost":
                            int.TryParse(nodeValue, out var magBoost);
                            item.MagicalSkillBoost = magBoost;
                            break;
                        case "magical_skill_boost_resist":
                            int.TryParse(nodeValue, out var magBoostRes);
                            item.MagicalSkillBoostResist = magBoostRes;
                            break;
                        case "magical_hit_accuracy":
                            int.TryParse(nodeValue, out var magHitAcc);
                            item.MagicalHitAccuracy = magHitAcc;
                            break;
                        case "attack_delay":
                            int.TryParse(nodeValue, out var atkDelay);
                            item.AttackDelay = atkDelay;
                            break;
                        case "hit_count":
                            int.TryParse(nodeValue, out var hitCount);
                            item.HitCount = hitCount;
                            break;
                        case "attack_gap":
                            float.TryParse(nodeValue, out var atkGap);
                            item.AttackGap = atkGap;
                            break;
                        case "attack_range":
                            float.TryParse(nodeValue, out var atkRange);
                            item.AttackRange = atkRange;
                            break;

                        // 布尔属性
                        case "lore":
                            item.Lore = ParseBoolean(nodeValue);
                            break;
                        case "can_exchange":
                            item.CanExchange = ParseBoolean(nodeValue);
                            break;
                        case "can_sell_to_npc":
                            item.CanSellToNpc = ParseBoolean(nodeValue);
                            break;
                        case "can_deposit_to_character_warehouse":
                            item.CanDepositToCharacterWarehouse = ParseBoolean(nodeValue);
                            break;
                        case "can_deposit_to_account_warehouse":
                            item.CanDepositToAccountWarehouse = ParseBoolean(nodeValue);
                            break;
                        case "can_deposit_to_guild_warehouse":
                            item.CanDepositToGuildWarehouse = ParseBoolean(nodeValue);
                            break;
                        case "breakable":
                            item.Breakable = ParseBoolean(nodeValue);
                            break;
                        case "soul_bind":
                            item.SoulBind = ParseBoolean(nodeValue);
                            break;
                        case "remove_when_logout":
                            item.RemoveWhenLogout = ParseBoolean(nodeValue);
                            break;
                        case "no_enchant":
                            item.NoEnchant = ParseBoolean(nodeValue);
                            break;
                        case "can_proc_enchant":
                            item.CanProcEnchant = ParseBoolean(nodeValue);
                            break;
                        case "can_composite_weapon":
                            item.CanCompositeWeapon = ParseBoolean(nodeValue);
                            break;
                        case "can_split":
                            item.CanSplit = ParseBoolean(nodeValue);
                            break;
                        case "item_drop_permitted":
                            item.ItemDropPermitted = ParseBoolean(nodeValue);
                            break;
                        case "can_ap_extraction":
                            item.CanApExtraction = ParseBoolean(nodeValue);
                            break;
                        case "can_polish":
                            item.CanPolish = ParseBoolean(nodeValue);
                            break;

                        // 字符串属性
                        case "equipment_slots":
                            item.EquipmentSlots = nodeValue;
                            break;
                        case "gender_permitted":
                            item.GenderPermitted = nodeValue;
                            break;
                        case "race_permitted":
                            item.RacePermitted = nodeValue;
                            break;
                        case "equip_bone":
                            item.EquipBone = nodeValue;
                            break;
                        case "bonus_apply":
                            item.BonusApply = nodeValue;
                            break;
                        case "trail_tex":
                            item.TrailTex = nodeValue;
                            break;

                        // 职业限制
                        case "warrior":
                            int.TryParse(nodeValue, out var warrior);
                            item.Warrior = warrior;
                            break;
                        case "scout":
                            int.TryParse(nodeValue, out var scout);
                            item.Scout = scout;
                            break;
                        case "mage":
                            int.TryParse(nodeValue, out var mage);
                            item.Mage = mage;
                            break;
                        case "cleric":
                            int.TryParse(nodeValue, out var cleric);
                            item.Cleric = cleric;
                            break;
                        case "engineer":
                            int.TryParse(nodeValue, out var engineer);
                            item.Engineer = engineer;
                            break;
                        case "artist":
                            int.TryParse(nodeValue, out var artist);
                            item.Artist = artist;
                            break;
                        case "fighter":
                            int.TryParse(nodeValue, out var fighter);
                            item.Fighter = fighter;
                            break;
                        case "knight":
                            int.TryParse(nodeValue, out var knight);
                            item.Knight = knight;
                            break;
                        case "assassin":
                            int.TryParse(nodeValue, out var assassin);
                            item.Assassin = assassin;
                            break;
                        case "ranger":
                            int.TryParse(nodeValue, out var ranger);
                            item.Ranger = ranger;
                            break;
                        case "wizard":
                            int.TryParse(nodeValue, out var wizard);
                            item.Wizard = wizard;
                            break;
                        case "elementalist":
                            int.TryParse(nodeValue, out var elementalist);
                            item.Elementalist = elementalist;
                            break;
                        case "chanter":
                            int.TryParse(nodeValue, out var chanter);
                            item.Chanter = chanter;
                            break;
                        case "priest":
                            int.TryParse(nodeValue, out var priest);
                            item.Priest = priest;
                            break;
                        case "gunner":
                            int.TryParse(nodeValue, out var gunner);
                            item.Gunner = gunner;
                            break;
                        case "bard":
                            int.TryParse(nodeValue, out var bard);
                            item.Bard = bard;
                            break;
                        case "rider":
                            int.TryParse(nodeValue, out var rider);
                            item.Rider = rider;
                            break;

                        // 其他数值属性
                        case "max_enchant_value":
                            int.TryParse(nodeValue, out var maxEnchant);
                            item.MaxEnchantValue = maxEnchant;
                            break;
                        case "option_slot_value":
                            int.TryParse(nodeValue, out var optSlot);
                            item.OptionSlotValue = optSlot;
                            break;
                        case "special_slot_value":
                            int.TryParse(nodeValue, out var specSlot);
                            item.SpecialSlotValue = specSlot;
                            break;
                        case "option_slot_bonus":
                            int.TryParse(nodeValue, out var optBonus);
                            item.OptionSlotBonus = optBonus;
                            break;
                        case "mesh_change":
                            int.TryParse(nodeValue, out var meshChange);
                            item.MeshChange = meshChange;
                            break;
                        case "dmg_decal":
                            int.TryParse(nodeValue, out var dmgDecal);
                            item.DmgDecal = dmgDecal;
                            break;
                        case "blade_fx":
                            int.TryParse(nodeValue, out var bladeFx);
                            item.BladeFx = bladeFx;
                            break;
                        case "polish_burn_on_attack":
                            int.TryParse(nodeValue, out var polishAtk);
                            item.PolishBurnOnAttack = polishAtk;
                            break;
                        case "polish_burn_on_defend":
                            int.TryParse(nodeValue, out var polishDef);
                            item.PolishBurnOnDefend = polishDef;
                            break;
                        case "cannot_changeskin":
                            int.TryParse(nodeValue, out var noChangeSkin);
                            item.CannotChangeskin = noChangeSkin;
                            break;
                        case "cash_item":
                            int.TryParse(nodeValue, out var cashItem);
                            item.CashItem = cashItem;
                            break;
                        case "bm_restrict_category":
                            int.TryParse(nodeValue, out var bmRestrict);
                            item.BmRestrictCategory = bmRestrict;
                            break;
                    }
                }

                return item;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析物品节点失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析布尔值（支持TRUE/FALSE和1/0）
        /// </summary>
        private bool ParseBoolean(string value)
        {
            if (string.IsNullOrEmpty(value))
                return false;

            value = value.ToUpper().Trim();

            return value == "TRUE" || value == "1";
        }
    }
}
