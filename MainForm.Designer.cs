namespace GameItemManager
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip1 = new MenuStrip();
            this.fileToolStripMenuItem = new ToolStripMenuItem();
            this.loadXmlFilesToolStripMenuItem = new ToolStripMenuItem();
            this.toolStripSeparator1 = new ToolStripSeparator();
            this.saveAllToolStripMenuItem = new ToolStripMenuItem();
            this.toolStripSeparator2 = new ToolStripSeparator();
            this.exitToolStripMenuItem = new ToolStripMenuItem();
            this.editToolStripMenuItem = new ToolStripMenuItem();
            this.addNewItemToolStripMenuItem = new ToolStripMenuItem();
            this.deleteItemToolStripMenuItem = new ToolStripMenuItem();
            this.toolStripSeparator3 = new ToolStripSeparator();
            this.findToolStripMenuItem = new ToolStripMenuItem();
            this.helpToolStripMenuItem = new ToolStripMenuItem();
            this.aboutToolStripMenuItem = new ToolStripMenuItem();
            
            this.toolStrip1 = new ToolStrip();
            this.toolStripButtonLoad = new ToolStripButton();
            this.toolStripButtonSave = new ToolStripButton();
            this.toolStripSeparator4 = new ToolStripSeparator();
            this.toolStripButtonAdd = new ToolStripButton();
            this.toolStripButtonEdit = new ToolStripButton();
            this.toolStripButtonDelete = new ToolStripButton();
            this.toolStripSeparator5 = new ToolStripSeparator();
            this.toolStripLabelSearch = new ToolStripLabel();
            this.toolStripTextBoxSearch = new ToolStripTextBox();
            this.toolStripButtonSearch = new ToolStripButton();
            this.toolStripButtonClearSearch = new ToolStripButton();
            
            this.statusStrip1 = new StatusStrip();
            this.toolStripStatusLabel1 = new ToolStripStatusLabel();
            this.toolStripStatusLabel2 = new ToolStripStatusLabel();
            this.toolStripProgressBar1 = new ToolStripProgressBar();
            
            this.splitContainer1 = new SplitContainer();
            this.splitContainer2 = new SplitContainer();
            this.groupBoxFiles = new GroupBox();
            this.listViewFiles = new ListView();
            this.columnHeaderFileName = new ColumnHeader();
            this.columnHeaderItemCount = new ColumnHeader();
            this.columnHeaderFileSize = new ColumnHeader();
            
            this.groupBoxItems = new GroupBox();
            this.listViewItems = new ListView();
            this.columnHeaderId = new ColumnHeader();
            this.columnHeaderName = new ColumnHeader();
            this.columnHeaderDisplayName = new ColumnHeader();
            this.columnHeaderWeaponType = new ColumnHeader();
            this.columnHeaderQuality = new ColumnHeader();
            this.columnHeaderLevel = new ColumnHeader();
            
            this.groupBoxDetails = new GroupBox();
            this.propertyGridItem = new PropertyGrid();
            
            this.SuspendLayout();
            
            // 
            // menuStrip1
            // 
            this.menuStrip1.Items.AddRange(new ToolStripItem[] {
                this.fileToolStripMenuItem,
                this.editToolStripMenuItem,
                this.helpToolStripMenuItem});
            this.menuStrip1.Location = new Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new Size(1200, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            
            // 
            // fileToolStripMenuItem
            // 
            this.fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.loadXmlFilesToolStripMenuItem,
                this.toolStripSeparator1,
                this.saveAllToolStripMenuItem,
                this.toolStripSeparator2,
                this.exitToolStripMenuItem});
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new Size(58, 20);
            this.fileToolStripMenuItem.Text = "文件(&F)";
            
            // 
            // loadXmlFilesToolStripMenuItem
            // 
            this.loadXmlFilesToolStripMenuItem.Name = "loadXmlFilesToolStripMenuItem";
            this.loadXmlFilesToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.O;
            this.loadXmlFilesToolStripMenuItem.Size = new Size(180, 22);
            this.loadXmlFilesToolStripMenuItem.Text = "加载XML文件(&L)";
            this.loadXmlFilesToolStripMenuItem.Click += new EventHandler(this.LoadXmlFilesToolStripMenuItem_Click);
            
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new Size(177, 6);
            
            // 
            // saveAllToolStripMenuItem
            // 
            this.saveAllToolStripMenuItem.Name = "saveAllToolStripMenuItem";
            this.saveAllToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.S;
            this.saveAllToolStripMenuItem.Size = new Size(180, 22);
            this.saveAllToolStripMenuItem.Text = "保存全部(&S)";
            this.saveAllToolStripMenuItem.Click += new EventHandler(this.SaveAllToolStripMenuItem_Click);
            
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new Size(177, 6);
            
            // 
            // exitToolStripMenuItem
            // 
            this.exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            this.exitToolStripMenuItem.Size = new Size(180, 22);
            this.exitToolStripMenuItem.Text = "退出(&X)";
            this.exitToolStripMenuItem.Click += new EventHandler(this.ExitToolStripMenuItem_Click);
            
            // 
            // editToolStripMenuItem
            // 
            this.editToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.addNewItemToolStripMenuItem,
                this.deleteItemToolStripMenuItem,
                this.toolStripSeparator3,
                this.findToolStripMenuItem});
            this.editToolStripMenuItem.Name = "editToolStripMenuItem";
            this.editToolStripMenuItem.Size = new Size(59, 20);
            this.editToolStripMenuItem.Text = "编辑(&E)";
            
            // 
            // addNewItemToolStripMenuItem
            // 
            this.addNewItemToolStripMenuItem.Name = "addNewItemToolStripMenuItem";
            this.addNewItemToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.N;
            this.addNewItemToolStripMenuItem.Size = new Size(180, 22);
            this.addNewItemToolStripMenuItem.Text = "新建物品(&N)";
            this.addNewItemToolStripMenuItem.Click += new EventHandler(this.AddNewItemToolStripMenuItem_Click);
            
            // 
            // deleteItemToolStripMenuItem
            // 
            this.deleteItemToolStripMenuItem.Name = "deleteItemToolStripMenuItem";
            this.deleteItemToolStripMenuItem.ShortcutKeys = Keys.Delete;
            this.deleteItemToolStripMenuItem.Size = new Size(180, 22);
            this.deleteItemToolStripMenuItem.Text = "删除物品(&D)";
            this.deleteItemToolStripMenuItem.Click += new EventHandler(this.DeleteItemToolStripMenuItem_Click);
            
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new Size(177, 6);
            
            // 
            // findToolStripMenuItem
            // 
            this.findToolStripMenuItem.Name = "findToolStripMenuItem";
            this.findToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.F;
            this.findToolStripMenuItem.Size = new Size(180, 22);
            this.findToolStripMenuItem.Text = "查找(&F)";
            this.findToolStripMenuItem.Click += new EventHandler(this.FindToolStripMenuItem_Click);
            
            // 
            // helpToolStripMenuItem
            // 
            this.helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.aboutToolStripMenuItem});
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new Size(61, 20);
            this.helpToolStripMenuItem.Text = "帮助(&H)";
            
            // 
            // aboutToolStripMenuItem
            // 
            this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            this.aboutToolStripMenuItem.Size = new Size(116, 22);
            this.aboutToolStripMenuItem.Text = "关于(&A)";
            this.aboutToolStripMenuItem.Click += new EventHandler(this.AboutToolStripMenuItem_Click);

            //
            // toolStrip1
            //
            this.toolStrip1.Items.AddRange(new ToolStripItem[] {
                this.toolStripButtonLoad,
                this.toolStripButtonSave,
                this.toolStripSeparator4,
                this.toolStripButtonAdd,
                this.toolStripButtonEdit,
                this.toolStripButtonDelete,
                this.toolStripSeparator5,
                this.toolStripLabelSearch,
                this.toolStripTextBoxSearch,
                this.toolStripButtonSearch,
                this.toolStripButtonClearSearch});
            this.toolStrip1.Location = new Point(0, 24);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new Size(1200, 25);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";

            //
            // toolStripButtonLoad
            //
            this.toolStripButtonLoad.DisplayStyle = ToolStripItemDisplayStyle.Image;
            this.toolStripButtonLoad.Image = SystemIcons.Application.ToBitmap();
            this.toolStripButtonLoad.ImageTransparentColor = Color.Magenta;
            this.toolStripButtonLoad.Name = "toolStripButtonLoad";
            this.toolStripButtonLoad.Size = new Size(23, 22);
            this.toolStripButtonLoad.Text = "加载XML文件";
            this.toolStripButtonLoad.Click += new EventHandler(this.LoadXmlFilesToolStripMenuItem_Click);

            //
            // toolStripButtonSave
            //
            this.toolStripButtonSave.DisplayStyle = ToolStripItemDisplayStyle.Image;
            this.toolStripButtonSave.Image = SystemIcons.Shield.ToBitmap();
            this.toolStripButtonSave.ImageTransparentColor = Color.Magenta;
            this.toolStripButtonSave.Name = "toolStripButtonSave";
            this.toolStripButtonSave.Size = new Size(23, 22);
            this.toolStripButtonSave.Text = "保存全部";
            this.toolStripButtonSave.Click += new EventHandler(this.SaveAllToolStripMenuItem_Click);

            //
            // toolStripSeparator4
            //
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new Size(6, 25);

            //
            // toolStripButtonAdd
            //
            this.toolStripButtonAdd.DisplayStyle = ToolStripItemDisplayStyle.Image;
            this.toolStripButtonAdd.Image = SystemIcons.Information.ToBitmap();
            this.toolStripButtonAdd.ImageTransparentColor = Color.Magenta;
            this.toolStripButtonAdd.Name = "toolStripButtonAdd";
            this.toolStripButtonAdd.Size = new Size(23, 22);
            this.toolStripButtonAdd.Text = "新建物品";
            this.toolStripButtonAdd.Click += new EventHandler(this.AddNewItemToolStripMenuItem_Click);

            //
            // toolStripButtonEdit
            //
            this.toolStripButtonEdit.DisplayStyle = ToolStripItemDisplayStyle.Image;
            this.toolStripButtonEdit.Image = SystemIcons.Exclamation.ToBitmap();
            this.toolStripButtonEdit.ImageTransparentColor = Color.Magenta;
            this.toolStripButtonEdit.Name = "toolStripButtonEdit";
            this.toolStripButtonEdit.Size = new Size(23, 22);
            this.toolStripButtonEdit.Text = "编辑物品";
            this.toolStripButtonEdit.Click += new EventHandler(this.EditItemButton_Click);

            //
            // toolStripButtonDelete
            //
            this.toolStripButtonDelete.DisplayStyle = ToolStripItemDisplayStyle.Image;
            this.toolStripButtonDelete.Image = SystemIcons.Error.ToBitmap();
            this.toolStripButtonDelete.ImageTransparentColor = Color.Magenta;
            this.toolStripButtonDelete.Name = "toolStripButtonDelete";
            this.toolStripButtonDelete.Size = new Size(23, 22);
            this.toolStripButtonDelete.Text = "删除物品";
            this.toolStripButtonDelete.Click += new EventHandler(this.DeleteItemToolStripMenuItem_Click);

            //
            // toolStripSeparator5
            //
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new Size(6, 25);

            //
            // toolStripLabelSearch
            //
            this.toolStripLabelSearch.Name = "toolStripLabelSearch";
            this.toolStripLabelSearch.Size = new Size(32, 22);
            this.toolStripLabelSearch.Text = "搜索:";

            //
            // toolStripTextBoxSearch
            //
            this.toolStripTextBoxSearch.Name = "toolStripTextBoxSearch";
            this.toolStripTextBoxSearch.Size = new Size(200, 25);
            this.toolStripTextBoxSearch.KeyPress += new KeyPressEventHandler(this.ToolStripTextBoxSearch_KeyPress);

            //
            // toolStripButtonSearch
            //
            this.toolStripButtonSearch.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.toolStripButtonSearch.Name = "toolStripButtonSearch";
            this.toolStripButtonSearch.Size = new Size(36, 22);
            this.toolStripButtonSearch.Text = "搜索";
            this.toolStripButtonSearch.Click += new EventHandler(this.ToolStripButtonSearch_Click);

            //
            // toolStripButtonClearSearch
            //
            this.toolStripButtonClearSearch.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.toolStripButtonClearSearch.Name = "toolStripButtonClearSearch";
            this.toolStripButtonClearSearch.Size = new Size(36, 22);
            this.toolStripButtonClearSearch.Text = "清空";
            this.toolStripButtonClearSearch.Click += new EventHandler(this.ToolStripButtonClearSearch_Click);

            //
            // statusStrip1
            //
            this.statusStrip1.Items.AddRange(new ToolStripItem[] {
                this.toolStripStatusLabel1,
                this.toolStripStatusLabel2,
                this.toolStripProgressBar1});
            this.statusStrip1.Location = new Point(0, 776);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new Size(1200, 24);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";

            //
            // toolStripStatusLabel1
            //
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new Size(32, 19);
            this.toolStripStatusLabel1.Text = "就绪";

            //
            // toolStripStatusLabel2
            //
            this.toolStripStatusLabel2.Name = "toolStripStatusLabel2";
            this.toolStripStatusLabel2.Size = new Size(68, 19);
            this.toolStripStatusLabel2.Text = "物品数量: 0";

            //
            // toolStripProgressBar1
            //
            this.toolStripProgressBar1.Name = "toolStripProgressBar1";
            this.toolStripProgressBar1.Size = new Size(100, 18);
            this.toolStripProgressBar1.Visible = false;

            //
            // splitContainer1
            //
            this.splitContainer1.Dock = DockStyle.Fill;
            this.splitContainer1.Location = new Point(0, 49);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = Orientation.Horizontal;
            this.splitContainer1.Panel1.Controls.Add(this.splitContainer2);
            this.splitContainer1.Panel2.Controls.Add(this.groupBoxDetails);
            this.splitContainer1.Size = new Size(1200, 727);
            this.splitContainer1.SplitterDistance = 400;
            this.splitContainer1.TabIndex = 3;

            //
            // splitContainer2
            //
            this.splitContainer2.Dock = DockStyle.Fill;
            this.splitContainer2.Location = new Point(0, 0);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Panel1.Controls.Add(this.groupBoxFiles);
            this.splitContainer2.Panel2.Controls.Add(this.groupBoxItems);
            this.splitContainer2.Size = new Size(1200, 400);
            this.splitContainer2.SplitterDistance = 300;
            this.splitContainer2.TabIndex = 0;

            //
            // groupBoxFiles
            //
            this.groupBoxFiles.Controls.Add(this.listViewFiles);
            this.groupBoxFiles.Dock = DockStyle.Fill;
            this.groupBoxFiles.Location = new Point(0, 0);
            this.groupBoxFiles.Name = "groupBoxFiles";
            this.groupBoxFiles.Size = new Size(300, 400);
            this.groupBoxFiles.TabIndex = 0;
            this.groupBoxFiles.TabStop = false;
            this.groupBoxFiles.Text = "XML文件列表";

            //
            // listViewFiles
            //
            this.listViewFiles.Columns.AddRange(new ColumnHeader[] {
                this.columnHeaderFileName,
                this.columnHeaderItemCount,
                this.columnHeaderFileSize});
            this.listViewFiles.Dock = DockStyle.Fill;
            this.listViewFiles.FullRowSelect = true;
            this.listViewFiles.GridLines = true;
            this.listViewFiles.Location = new Point(3, 19);
            this.listViewFiles.MultiSelect = false;
            this.listViewFiles.Name = "listViewFiles";
            this.listViewFiles.Size = new Size(294, 378);
            this.listViewFiles.TabIndex = 0;
            this.listViewFiles.UseCompatibleStateImageBehavior = false;
            this.listViewFiles.View = View.Details;
            this.listViewFiles.SelectedIndexChanged += new EventHandler(this.ListViewFiles_SelectedIndexChanged);

            //
            // columnHeaderFileName
            //
            this.columnHeaderFileName.Text = "文件名";
            this.columnHeaderFileName.Width = 150;

            //
            // columnHeaderItemCount
            //
            this.columnHeaderItemCount.Text = "物品数量";
            this.columnHeaderItemCount.Width = 80;

            //
            // columnHeaderFileSize
            //
            this.columnHeaderFileSize.Text = "文件大小";
            this.columnHeaderFileSize.Width = 80;

            //
            // groupBoxItems
            //
            this.groupBoxItems.Controls.Add(this.listViewItems);
            this.groupBoxItems.Dock = DockStyle.Fill;
            this.groupBoxItems.Location = new Point(0, 0);
            this.groupBoxItems.Name = "groupBoxItems";
            this.groupBoxItems.Size = new Size(896, 400);
            this.groupBoxItems.TabIndex = 0;
            this.groupBoxItems.TabStop = false;
            this.groupBoxItems.Text = "物品列表";

            //
            // listViewItems
            //
            this.listViewItems.Columns.AddRange(new ColumnHeader[] {
                this.columnHeaderId,
                this.columnHeaderName,
                this.columnHeaderDisplayName,
                this.columnHeaderWeaponType,
                this.columnHeaderQuality,
                this.columnHeaderLevel});
            this.listViewItems.Dock = DockStyle.Fill;
            this.listViewItems.FullRowSelect = true;
            this.listViewItems.GridLines = true;
            this.listViewItems.Location = new Point(3, 19);
            this.listViewItems.MultiSelect = false;
            this.listViewItems.Name = "listViewItems";
            this.listViewItems.Size = new Size(890, 378);
            this.listViewItems.TabIndex = 0;
            this.listViewItems.UseCompatibleStateImageBehavior = false;
            this.listViewItems.View = View.Details;
            this.listViewItems.SelectedIndexChanged += new EventHandler(this.ListViewItems_SelectedIndexChanged);
            this.listViewItems.DoubleClick += new EventHandler(this.ListViewItems_DoubleClick);

            //
            // columnHeaderId
            //
            this.columnHeaderId.Text = "ID";
            this.columnHeaderId.Width = 100;

            //
            // columnHeaderName
            //
            this.columnHeaderName.Text = "名称";
            this.columnHeaderName.Width = 150;

            //
            // columnHeaderDisplayName
            //
            this.columnHeaderDisplayName.Text = "显示名称";
            this.columnHeaderDisplayName.Width = 200;

            //
            // columnHeaderWeaponType
            //
            this.columnHeaderWeaponType.Text = "武器类型";
            this.columnHeaderWeaponType.Width = 120;

            //
            // columnHeaderQuality
            //
            this.columnHeaderQuality.Text = "品质";
            this.columnHeaderQuality.Width = 80;

            //
            // columnHeaderLevel
            //
            this.columnHeaderLevel.Text = "等级";
            this.columnHeaderLevel.Width = 60;

            //
            // groupBoxDetails
            //
            this.groupBoxDetails.Controls.Add(this.propertyGridItem);
            this.groupBoxDetails.Dock = DockStyle.Fill;
            this.groupBoxDetails.Location = new Point(0, 0);
            this.groupBoxDetails.Name = "groupBoxDetails";
            this.groupBoxDetails.Size = new Size(1200, 323);
            this.groupBoxDetails.TabIndex = 0;
            this.groupBoxDetails.TabStop = false;
            this.groupBoxDetails.Text = "物品详细信息";

            //
            // propertyGridItem
            //
            this.propertyGridItem.Dock = DockStyle.Fill;
            this.propertyGridItem.Location = new Point(3, 19);
            this.propertyGridItem.Name = "propertyGridItem";
            this.propertyGridItem.Size = new Size(1194, 301);
            this.propertyGridItem.TabIndex = 0;
            this.propertyGridItem.PropertyValueChanged += new PropertyValueChangedEventHandler(this.PropertyGridItem_PropertyValueChanged);

            //
            // MainForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "MainForm";
            this.Text = "游戏物品管理器";
            this.WindowState = FormWindowState.Maximized;
            this.Load += new EventHandler(this.MainForm_Load);

            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).EndInit();
            this.splitContainer2.ResumeLayout(false);
            this.groupBoxFiles.ResumeLayout(false);
            this.groupBoxItems.ResumeLayout(false);
            this.groupBoxDetails.ResumeLayout(false);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem loadXmlFilesToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripMenuItem saveAllToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem editToolStripMenuItem;
        private ToolStripMenuItem addNewItemToolStripMenuItem;
        private ToolStripMenuItem deleteItemToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator3;
        private ToolStripMenuItem findToolStripMenuItem;
        private ToolStripMenuItem helpToolStripMenuItem;
        private ToolStripMenuItem aboutToolStripMenuItem;
        
        private ToolStrip toolStrip1;
        private ToolStripButton toolStripButtonLoad;
        private ToolStripButton toolStripButtonSave;
        private ToolStripSeparator toolStripSeparator4;
        private ToolStripButton toolStripButtonAdd;
        private ToolStripButton toolStripButtonEdit;
        private ToolStripButton toolStripButtonDelete;
        private ToolStripSeparator toolStripSeparator5;
        private ToolStripLabel toolStripLabelSearch;
        private ToolStripTextBox toolStripTextBoxSearch;
        private ToolStripButton toolStripButtonSearch;
        private ToolStripButton toolStripButtonClearSearch;
        
        private StatusStrip statusStrip1;
        private ToolStripStatusLabel toolStripStatusLabel1;
        private ToolStripStatusLabel toolStripStatusLabel2;
        private ToolStripProgressBar toolStripProgressBar1;
        
        private SplitContainer splitContainer1;
        private SplitContainer splitContainer2;
        private GroupBox groupBoxFiles;
        private ListView listViewFiles;
        private ColumnHeader columnHeaderFileName;
        private ColumnHeader columnHeaderItemCount;
        private ColumnHeader columnHeaderFileSize;
        
        private GroupBox groupBoxItems;
        private ListView listViewItems;
        private ColumnHeader columnHeaderId;
        private ColumnHeader columnHeaderName;
        private ColumnHeader columnHeaderDisplayName;
        private ColumnHeader columnHeaderWeaponType;
        private ColumnHeader columnHeaderQuality;
        private ColumnHeader columnHeaderLevel;
        
        private GroupBox groupBoxDetails;
        private PropertyGrid propertyGridItem;
    }
}
