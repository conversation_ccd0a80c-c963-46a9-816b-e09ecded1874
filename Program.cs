using System.Runtime.InteropServices;
using System.Text;

namespace GameItemManager
{
    internal static class Program
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool AllocConsole();

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 分配控制台窗口用于调试
            AllocConsole();
            Console.WriteLine("游戏物品管理器调试控制台");
            Console.WriteLine("========================");

            // 注册编码提供程序以支持UTF-16等编码
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 启用应用程序视觉样式
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 运行主窗体
            Application.Run(new MainForm());
        }
    }
}
