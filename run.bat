@echo off
echo 游戏物品管理器启动脚本
echo ========================

REM 检查是否安装了.NET 6.0
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到.NET 6.0运行时
    echo 请从以下地址下载并安装.NET 6.0:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo 正在启动游戏物品管理器...
echo.

REM 编译并运行程序
dotnet run GameItemManager.csproj

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出错，错误代码: %errorlevel%
    echo 请检查项目文件是否完整
    pause
)
