using GameItemManager.Models;

namespace GameItemManager.Forms
{
    /// <summary>
    /// 高级搜索窗体
    /// </summary>
    public partial class AdvancedSearchForm : Form
    {
        public string SearchText { get; private set; } = string.Empty;
        public string SelectedQuality { get; private set; } = string.Empty;
        public string SelectedWeaponType { get; private set; } = string.Empty;
        public string SelectedItemType { get; private set; } = string.Empty;
        public int MinLevel { get; private set; } = 0;
        public int MaxLevel { get; private set; } = 999;
        public bool SearchInDescription { get; private set; } = true;
        public bool SearchInName { get; private set; } = true;
        public bool SearchInId { get; private set; } = true;
        
        public AdvancedSearchForm()
        {
            InitializeComponent();
            InitializeUI();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            this.Text = "高级搜索";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            
            // 初始化下拉框数据
            InitializeComboBoxes();
        }
        
        /// <summary>
        /// 初始化下拉框数据
        /// </summary>
        private void InitializeComboBoxes()
        {
            // 品质选项
            comboBoxQuality.Items.AddRange(new string[] 
            {
                "全部", "common", "rare", "unique", "epic", "legendary"
            });
            comboBoxQuality.SelectedIndex = 0;
            
            // 武器类型选项
            comboBoxWeaponType.Items.AddRange(new string[] 
            {
                "全部", "1h_sword", "2h_sword", "dagger", "mace", "orb", 
                "book", "polearm", "staff", "bow"
            });
            comboBoxWeaponType.SelectedIndex = 0;
            
            // 物品类型选项
            comboBoxItemType.Items.AddRange(new string[] 
            {
                "全部", "normal", "abyss", "draconic"
            });
            comboBoxItemType.SelectedIndex = 0;
            
            // 设置等级范围默认值
            numericUpDownMinLevel.Value = 0;
            numericUpDownMaxLevel.Value = 999;
            
            // 设置搜索选项默认值
            checkBoxSearchInId.Checked = true;
            checkBoxSearchInName.Checked = true;
            checkBoxSearchInDescription.Checked = true;
        }
        
        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void ButtonSearch_Click(object sender, EventArgs e)
        {
            // 获取搜索条件
            SearchText = textBoxSearchText.Text.Trim();
            SelectedQuality = comboBoxQuality.SelectedItem?.ToString() ?? "全部";
            SelectedWeaponType = comboBoxWeaponType.SelectedItem?.ToString() ?? "全部";
            SelectedItemType = comboBoxItemType.SelectedItem?.ToString() ?? "全部";
            MinLevel = (int)numericUpDownMinLevel.Value;
            MaxLevel = (int)numericUpDownMaxLevel.Value;
            SearchInId = checkBoxSearchInId.Checked;
            SearchInName = checkBoxSearchInName.Checked;
            SearchInDescription = checkBoxSearchInDescription.Checked;
            
            // 验证搜索条件
            if (MinLevel > MaxLevel)
            {
                MessageBox.Show("最小等级不能大于最大等级", "输入错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            if (!SearchInId && !SearchInName && !SearchInDescription && string.IsNullOrEmpty(SearchText))
            {
                MessageBox.Show("请至少选择一个搜索范围或输入搜索文本", "输入错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void ButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void ButtonReset_Click(object sender, EventArgs e)
        {
            textBoxSearchText.Text = string.Empty;
            comboBoxQuality.SelectedIndex = 0;
            comboBoxWeaponType.SelectedIndex = 0;
            comboBoxItemType.SelectedIndex = 0;
            numericUpDownMinLevel.Value = 0;
            numericUpDownMaxLevel.Value = 999;
            checkBoxSearchInId.Checked = true;
            checkBoxSearchInName.Checked = true;
            checkBoxSearchInDescription.Checked = true;
        }
        
        /// <summary>
        /// 根据搜索条件过滤物品
        /// </summary>
        public List<GameItem> FilterItems(List<GameItem> items)
        {
            var filteredItems = items.AsEnumerable();
            
            // 按文本搜索
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchText = SearchText.ToLower();
                filteredItems = filteredItems.Where(item =>
                {
                    bool match = false;
                    
                    if (SearchInId && item.Id.ToLower().Contains(searchText))
                        match = true;
                    if (SearchInName && item.Name.ToLower().Contains(searchText))
                        match = true;
                    if (SearchInDescription && 
                        (item.Desc.ToLower().Contains(searchText) || 
                         item.DescText.ToLower().Contains(searchText)))
                        match = true;
                        
                    return match;
                });
            }
            
            // 按品质过滤
            if (SelectedQuality != "全部")
            {
                filteredItems = filteredItems.Where(item => 
                    item.Quality.Equals(SelectedQuality, StringComparison.OrdinalIgnoreCase));
            }
            
            // 按武器类型过滤
            if (SelectedWeaponType != "全部")
            {
                filteredItems = filteredItems.Where(item => 
                    item.WeaponType.Equals(SelectedWeaponType, StringComparison.OrdinalIgnoreCase));
            }
            
            // 按物品类型过滤
            if (SelectedItemType != "全部")
            {
                filteredItems = filteredItems.Where(item => 
                    item.ItemType.Equals(SelectedItemType, StringComparison.OrdinalIgnoreCase));
            }
            
            // 按等级范围过滤
            filteredItems = filteredItems.Where(item => 
                item.Level >= MinLevel && item.Level <= MaxLevel);
            
            return filteredItems.ToList();
        }
    }
}
