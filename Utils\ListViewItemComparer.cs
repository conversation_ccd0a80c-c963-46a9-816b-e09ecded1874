using System.Collections;

namespace GameItemManager.Utils
{
    /// <summary>
    /// ListView项目比较器，用于排序
    /// </summary>
    public class ListViewItemComparer : IComparer
    {
        private int _columnIndex;
        private SortOrder _sortOrder;
        
        public ListViewItemComparer(int columnIndex, SortOrder sortOrder = SortOrder.Ascending)
        {
            _columnIndex = columnIndex;
            _sortOrder = sortOrder;
        }
        
        public int Compare(object? x, object? y)
        {
            if (x is not ListViewItem itemX || y is not ListViewItem itemY)
                return 0;
                
            string textX = itemX.SubItems[_columnIndex].Text;
            string textY = itemY.SubItems[_columnIndex].Text;
            
            int result;
            
            // 尝试按数字比较
            if (int.TryParse(textX, out int numX) && int.TryParse(textY, out int numY))
            {
                result = numX.CompareTo(numY);
            }
            else
            {
                // 按字符串比较
                result = string.Compare(textX, textY, StringComparison.OrdinalIgnoreCase);
            }
            
            // 根据排序顺序调整结果
            if (_sortOrder == SortOrder.Descending)
                result = -result;
                
            return result;
        }
        
        public void SetSortOrder(SortOrder sortOrder)
        {
            _sortOrder = sortOrder;
        }
        
        public void SetColumnIndex(int columnIndex)
        {
            _columnIndex = columnIndex;
        }
    }
}
