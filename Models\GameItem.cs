namespace GameItemManager.Models
{
    /// <summary>
    /// 游戏物品数据模型
    /// </summary>
    public class GameItem
    {
        // 基本信息
        public string Id { get; set; } = string.Empty;                    // 物品ID
        public string Name { get; set; } = string.Empty;                  // 物品名称
        public string Desc { get; set; } = string.Empty;                  // 描述字符串ID
        public string DescText { get; set; } = string.Empty;              // 描述中文文本
        
        // 物品类型
        public string WeaponType { get; set; } = string.Empty;            // 武器类型
        public string ItemType { get; set; } = string.Empty;              // 物品类型
        public string AttackType { get; set; } = string.Empty;            // 攻击类型
        public string Quality { get; set; } = string.Empty;               // 品质
        
        // 外观相关
        public string Mesh { get; set; } = string.Empty;                  // 模型
        public string Material { get; set; } = string.Empty;              // 材质
        public string IconName { get; set; } = string.Empty;              // 图标名称
        public string UiSoundType { get; set; } = string.Empty;           // UI音效
        
        // 数值属性
        public int Level { get; set; }                                     // 等级
        public int Price { get; set; }                                     // 价格
        public int MaxStackCount { get; set; } = 1;                       // 最大堆叠数量
        public int MinDamage { get; set; }                                 // 最小伤害
        public int MaxDamage { get; set; }                                 // 最大伤害
        
        // 基础属性
        public int Str { get; set; }                                       // 力量
        public int Agi { get; set; }                                       // 敏捷
        public int Kno { get; set; }                                       // 知识
        public int Vit { get; set; }                                       // 体质
        public int Dex { get; set; }                                       // 灵巧
        public int Wil { get; set; }                                       // 意志
        
        // 战斗属性
        public int HitAccuracy { get; set; }                              // 命中精度
        public int Critical { get; set; }                                 // 暴击
        public int Parry { get; set; }                                     // 格挡
        public int MagicalSkillBoost { get; set; }                        // 魔法技能增强
        public int MagicalSkillBoostResist { get; set; }                  // 魔法技能增强抗性
        public int MagicalHitAccuracy { get; set; }                       // 魔法命中精度
        
        // 攻击相关
        public int AttackDelay { get; set; }                              // 攻击延迟
        public int HitCount { get; set; }                                  // 攻击次数
        public float AttackGap { get; set; }                              // 攻击间隔
        public float AttackRange { get; set; }                            // 攻击范围
        
        // 布尔属性
        public bool Lore { get; set; }                                     // 传说
        public bool CanExchange { get; set; } = true;                     // 可交易
        public bool CanSellToNpc { get; set; } = true;                    // 可卖给NPC
        public bool CanDepositToCharacterWarehouse { get; set; } = true;  // 可存入角色仓库
        public bool CanDepositToAccountWarehouse { get; set; } = true;    // 可存入账户仓库
        public bool CanDepositToGuildWarehouse { get; set; } = true;      // 可存入公会仓库
        public bool Breakable { get; set; } = true;                       // 可损坏
        public bool SoulBind { get; set; }                                 // 灵魂绑定
        public bool RemoveWhenLogout { get; set; }                         // 登出时移除
        public bool NoEnchant { get; set; }                                // 不可附魔
        public bool CanProcEnchant { get; set; } = true;                  // 可进行附魔
        public bool CanCompositeWeapon { get; set; } = true;              // 可合成武器
        public bool CanSplit { get; set; }                                 // 可分割
        public bool ItemDropPermitted { get; set; }                       // 允许掉落
        public bool CanApExtraction { get; set; }                         // 可AP提取
        public bool CanPolish { get; set; } = true;                       // 可抛光
        
        // 其他属性
        public string EquipmentSlots { get; set; } = string.Empty;        // 装备槽位
        public string GenderPermitted { get; set; } = "all";              // 性别限制
        public string RacePermitted { get; set; } = "pc_light pc_dark";   // 种族限制
        public string EquipBone { get; set; } = string.Empty;             // 装备骨骼
        public string BonusApply { get; set; } = "equip";                 // 奖励应用
        
        // 职业限制 (1表示可用，0表示不可用)
        public int Warrior { get; set; } = 1;                             // 战士
        public int Scout { get; set; } = 1;                               // 侦察兵
        public int Mage { get; set; } = 1;                                 // 法师
        public int Cleric { get; set; } = 1;                              // 牧师
        public int Engineer { get; set; } = 1;                            // 工程师
        public int Artist { get; set; } = 1;                              // 艺术家
        public int Fighter { get; set; } = 1;                             // 格斗家
        public int Knight { get; set; } = 1;                              // 骑士
        public int Assassin { get; set; } = 1;                            // 刺客
        public int Ranger { get; set; } = 1;                              // 游侠
        public int Wizard { get; set; } = 1;                              // 巫师
        public int Elementalist { get; set; } = 1;                        // 元素师
        public int Chanter { get; set; } = 1;                             // 吟游诗人
        public int Priest { get; set; } = 1;                              // 祭司
        public int Gunner { get; set; } = 1;                              // 枪手
        public int Bard { get; set; } = 1;                                 // 吟游诗人
        public int Rider { get; set; } = 1;                               // 骑手
        
        // 附魔相关
        public int MaxEnchantValue { get; set; } = 15;                     // 最大附魔值
        public int OptionSlotValue { get; set; }                          // 选项槽值
        public int SpecialSlotValue { get; set; }                         // 特殊槽值
        public int OptionSlotBonus { get; set; }                          // 选项槽奖励
        
        // 特效相关
        public int MeshChange { get; set; }                               // 模型变化
        public int DmgDecal { get; set; } = 1;                            // 伤害贴花
        public int BladeFx { get; set; } = 1;                             // 刀刃特效
        public string TrailTex { get; set; } = "fxtrail_normal";          // 轨迹纹理
        
        // 抛光相关
        public int PolishBurnOnAttack { get; set; } = 29;                 // 攻击时抛光消耗
        public int PolishBurnOnDefend { get; set; } = 12;                 // 防御时抛光消耗
        
        // 其他
        public int CannotChangeskin { get; set; }                         // 不可换肤
        public int CashItem { get; set; }                                 // 现金物品
        public int BmRestrictCategory { get; set; } = 1;                  // BM限制类别
        
        /// <summary>
        /// 获取物品的显示名称（优先使用中文描述）
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(DescText) ? DescText : Name;
        
        /// <summary>
        /// 获取物品的完整显示信息
        /// </summary>
        public string FullDisplayInfo => $"[{Id}] {DisplayName}";
    }
}
